const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')
const path = require('path')

ipcRenderer.on('is-maximized', (event, isMaximized) => {
    console.log(isMaximized, 'isMaximized')
})
contextBridge.exposeInMainWorld(
    'electronAPI',
    {
        minimize: () => ipcRenderer.send('minimize'),
        maximize: () => ipcRenderer.send('maximize'),
        close: () => ipcRenderer.send('close'),
        isMaximized: () => ipcRenderer.invoke('is-maximized'),
        toggleMaximize: () => ipcRenderer.send('toggle-maximize'),
        SwitchTop: () => ipcRenderer.send('switch-top'),
        isOnTop: () => ipcRenderer.invoke('is-on-top'),
        path: path,
        fixedSize: ({ width, height }) => ipcRenderer.send('fixed-size', { width, height }),
        normalSize: () => ipc<PERSON>enderer.send('normal-size'),
        openNewWindow: (url, { width, height, resizable }) => ipcRenderer.send('open-new-window', url, { width, height, resizable })
    }
)