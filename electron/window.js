const { BrowserWindow, app, Menu, ipcMain } = require("electron")
const path = require("path")
const { Tray } = require("electron")
const { mainWindow } = require("./main")
function initTray(win) {
    const tray = new Tray(path.join(__dirname, '/assets/Ship.png'))
    const contextMenu = Menu.buildFromTemplate([
        {
            label: '显示/隐藏',
            click: () => {
                if (!win.isVisible()) {
                    win.show()
                }
            }
        },
        {
            label: '退出',
            click: () => {
                app.exit(0)
            }
        }
    ])
    tray.on('click', () => {
        win.show()
    })
    tray.setContextMenu(contextMenu)
}

function initMenu(win) {
    let env = process.env.NODE_ENV
    let template = []
    // if (env === 'development') {
    template = [
        {
            label: '窗口',
            submenu: [
                {
                    label: '重新载入',
                    role: 'reload'
                },
                {
                    label: '开发者工具',
                    role: 'toggleDevTools'
                }
            ]
        }
    ]
    // }
    const menu = Menu.buildFromTemplate(template)
    Menu.setApplicationMenu(menu)
}
function subscribeWindowEvents() {
    ipcMain.on('minimize', (e) => {
        const win = BrowserWindow.fromWebContents(e.sender)
        win.minimize()
    })
    ipcMain.on('maximize', (e) => {
        const win = BrowserWindow.fromWebContents(e.sender)
        win.maximize()
    })
    ipcMain.on('close', (e) => {
        const win = BrowserWindow.fromWebContents(e.sender)
        win.close()
        if (win === mainWindow) {
            const wins = BrowserWindow.getAllWindows()
            wins.forEach(win => {
                win.close()
            })
            app.quit()
        }
    })
    ipcMain.on('toggle-maximize', (e) => {
        const win = BrowserWindow.fromWebContents(e.sender)
        win.isMaximized() ? win.unmaximize() : win.maximize()
    })
    ipcMain.handle('is-maximized', (e) => {
        const win = BrowserWindow.fromWebContents(e.sender)
        return win.isMaximized()
    })
    ipcMain.on('switch-top', (e) => {
        const win = BrowserWindow.fromWebContents(e.sender)
        win.setAlwaysOnTop(!win.isAlwaysOnTop())
    })
    ipcMain.handle('is-on-top', (e) => {
        const win = BrowserWindow.fromWebContents(e.sender)
        return win.isAlwaysOnTop()
    })
    ipcMain.on('fixed-size', (e, { width = 500, height = 400 }) => {
        const win = BrowserWindow.fromWebContents(e.sender)
        // 临时移除最小尺寸限制，允许窗口缩小到登录页尺寸
        win.setMinimumSize(width, height)
        win.setSize(width, height)
        win.setResizable(false)
        // 居中显示
        win.center()
    })
    ipcMain.on('normal-size', (e) => {
        const win = BrowserWindow.fromWebContents(e.sender)
        win.setMinimumSize(1000, 800)
        win.setSize(1200, 800)
        win.setResizable(false)  // 保持不可调节大小
        win.center()
    })
    ipcMain.on('open-new-window', (e, url, { width = 1200, height = 800, resizable = false }) => {
        const win = new BrowserWindow({
            width,
            height,
            frame: false,
            resizable,
            // parent: BrowserWindow.fromWebContents(e.sender),
            // show: false,
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: true, // 可以使用require方法
                enableRemoteModule: true, // 可以使用remote方法
                webSecurity: false,
                preload: path.join(__dirname, 'preload.js'),
            },
        })
        let env = process.env.NODE_ENV
        if (env === 'development') {
            // 热更新监听窗口
            win.loadURL(`http://localhost:3001#${url}`)
            // 打开开发工具
            // mainWindow.webContents.openDevTools()

        } else {
            const indexPath = path.resolve(__dirname, '../dist/index.html')
            win.loadURL(`file://${indexPath}#${url}`)
        }
        win.once('ready-to-show', () => {
            win.show()
        })
    })
}
function createMainWindow() {
    const win = new BrowserWindow({
        width: 1200,
        height: 800,
        frame: false,
        show: false,
        minWidth: 1000,
        minHeight: 800,
        // resizable: false,
        // transparent: true,
        hasShadow: true, // 启用系统阴影
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: true, // 可以使用require方法
            enableRemoteModule: true, // 可以使用remote方法
            webSecurity: false,
            preload: path.join(__dirname, 'preload.js'),
        },
        icon: path.join(__dirname, '/assets/Ship.png')
    })
    win.setHasShadow(true)
    initTray(win)
    initMenu(win)
    subscribeWindowEvents()
    return win
}
module.exports = {
    createMainWindow
}