const { app, BrowserWindow, ipcMain } = require('electron')
const { createMainWindow } = require('./window.js')
const { checkServer, cleanupJavaProcess, getJavaPath } = require('./backend.js')
const path = require('path')
const fs = require('fs')
const { exec, spawn } = require('child_process')
const SERVER_CONFIG = {
  PORT: 9000,
  HOST: 'localhost',
  TIMEOUT: 10000,
  MAX_RETRIES: 3,
  RETRY_INTERVAL: 2000
}
// 初始化路径
const { jarPath, javaExecutable, env } = getJavaPath()
let javaProcess = null
let mainWindow
// 配置常量
// const electronReload = require('electron-reload'
async function createWindow() {
  mainWindow = createMainWindow()
  // 检查JAR文件是否存在
  if (!fs.existsSync(jarPath)) {
    console.error(`JAR文件不存在: ${jarPath}`)
    // 不退出应用，而是显示错误页面
    const errorHtml = `
      <html>
        <head><title>AdminDemo - 错误</title></head>
        <body style="font-family: Arial; padding: 20px; background: #f5f5f5;">
          <h1 style="color: #d32f2f;">启动失败</h1>
          <p><strong>错误原因：</strong>Java后端文件缺失</p>
          <p><strong>缺失文件：</strong>${jarPath}</p>
          <p><strong>解决方案：</strong>请确保java文件夹已正确打包</p>
          <hr>
          <p><em>应用路径：${__dirname}</em></p>
        </body>
      </html>
    `
    mainWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(errorHtml))
    mainWindow.show()
    return
  }
  try {
    let env = process.env.NODE_ENV

    if (env === 'development') {
      // 热更新监听窗口
      mainWindow.loadURL('http://localhost:3001')
      // 打开开发工具
      // mainWindow.webContents.openDevTools()
    } else {
      mainWindow.loadFile(path.resolve(__dirname, '../dist/index.html')) // 新增
    }
    javaProcess = await checkServer(SERVER_CONFIG.PORT, SERVER_CONFIG.HOST, SERVER_CONFIG.TIMEOUT, SERVER_CONFIG.MAX_RETRIES, javaExecutable, jarPath)
    mainWindow.show()
  } catch (error) {
    // 不退出应用，显示错误信息
    const errorHtml = `
      <html>
        <head><title>AdminDemo - 错误</title></head>
        <body style="font-family: Arial; padding: 20px; background: #f5f5f5;">
          <h1 style="color: #d32f2f;">服务器启动失败</h1>
          <p><strong>错误信息：</strong>${error.message}</p>
          <p><strong>可能原因：</strong></p>
          <ul>
            <li>Java环境未正确配置</li>
            <li>端口${SERVER_CONFIG.PORT}被占用</li>
            <li>Java后端服务启动失败</li>
          </ul>
          <button onclick="location.reload()" style="padding: 10px 20px; background: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer;">
            重新尝试
          </button>
          <hr>
          <details>
            <summary>详细信息</summary>
            <pre>${error.stack || error.toString()}</pre>
          </details>
        </body>
      </html>
    `
    mainWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(errorHtml))
    mainWindow.show()
  }
}

app.whenReady().then(() => {
  createWindow()
  app.on('activate', function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

app.on('will-quit', async function () {
  await cleanupJavaProcess()
})
module.exports = {
  mainWindow
}
