## xmlsoft.org: libxslt v1.1.39

### libxslt License
```

Licence for libxslt except libexslt
----------------------------------------------------------------------
 Copyright (C) 2001-2002 <PERSON>.  All Rights Reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is fur-
nished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FIT-
NESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE
DANIEL VEILLARD BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CON-
NECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the name of Daniel Veillard shall not
be used in advertising or otherwise to promote the sale, use or other deal-
ings in this Software without prior written authorization from him.

----------------------------------------------------------------------

Licence for libexslt
----------------------------------------------------------------------
 Copyright (C) 2001-2002 Thomas Broyer, Charlie Bozeman and Daniel Veillard.
 All Rights Reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is fur-
nished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FIT-
NESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE
AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CON-
NECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the name of the authors shall not
be used in advertising or otherwise to promote the sale, use or other deal-
ings in this Software without prior written authorization from him.
----------------------------------------------------------------------

=== copyright notices for trio.h ===
 * Copyright (C) 1998 Bjorn Reese and Daniel Stenberg.
=== copyright notices for triodef.h ===
 * Copyright (C) 2001 Bjorn Reese <<EMAIL>>
=== license text for trio.h and triodef.h ===
 *
 * Permission to use, copy, modify, and distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
 * WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE. THE AUTHORS AND
 * CONTRIBUTORS ACCEPT NO RESPONSIBILITY IN ANY CONCEIVABLE MANNER.
 *

```

### AUTHORS File Information
```
LIBXSLT REFERENCE LIBRARY AUTHORS
=============================

This is the list of LIBXSLT Reference Library ("libxslt") Contributing
Authors, for copyright and licensing purposes.

Daniel Veillard:
   <EMAIL>
   DV on #gnome IRC channel
   http://veillard.com/
   Used to work at W3C, now Red Hat
   co-chair of W3C XML Linking WG
   invited expert on the W3C XML Core WG
   Author of libxml2 upon which this library is based.

Bjorn Reese:
   <EMAIL>
   http://home1.stofanet.dk/breese/
   Software developer at http://www.systematic.dk/
   Member of the XML-MTF Mapping WG.

William Brack <<EMAIL>>

Thomas Broyer <<EMAIL>>

Igor Zlatkovic <<EMAIL>> for the Windows port

Patches gently provided by a multitude of people :

Abhishek Arya <<EMAIL>>
Ben Walton <<EMAIL>>
Bjorn Reese <<EMAIL>>
C. M. Sperberg-McQueen <<EMAIL>>
Colin Walters <<EMAIL>>
Daniel Mustieles <<EMAIL>>
Daniel Richard G <<EMAIL>>
Darin Adler <<EMAIL>>
ÉRDI Gergo <<EMAIL>>
Fatih Demir <<EMAIL>>
Federico Mena Quintero <<EMAIL>>
Frederic Crozat <<EMAIL>>
Hao Hu <<EMAIL>>
Havoc Pennington <<EMAIL>>
IlyaS <<EMAIL>>
jacob berkman <<EMAIL>>
Jason Viers <<EMAIL>>
Jérôme Carretero <<EMAIL>>
Joachim Breitner <<EMAIL>>
Johan Dahlin <<EMAIL>>
John Fleck <<EMAIL>>
Jose Maria Celorio <<EMAIL>>
Julio M. Merino Vidal <<EMAIL>>
Kasimier T. Buchcik <<EMAIL>>
Kjartan Maraas <<EMAIL>>
Laurence Rowe <<EMAIL>>
Malcolm Purvis <<EMAIL>>
Martin <<EMAIL>>
Michael Bonfils <<EMAIL>>
Mike Hommey <<EMAIL>>
money_seshu Dronamraju <<EMAIL>>
Nick Wellnhofer <<EMAIL>>
Nix <<EMAIL>>
Pedro F. Giffuni <<EMAIL>>
Peter Williams <<EMAIL>>
Rob Richards <<EMAIL>>
Roumen Petrov <<EMAIL>>
Stefan Kost <<EMAIL>>
Tomasz Kłoczko <<EMAIL>>
Chris Evans <<EMAIL>>

```
