#
# Copyright (c) 2004, 2016, Oracle and/or its affiliates. All rights reserved.
# ORACLE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
#

error.internal.badmsg=erro interno, mensagem desconhecida
error.badinst.nojre=Instala\u00E7\u00E3o incorreta. Nenhum JRE encontrado no arquivo de configura\u00E7\u00E3o
error.launch.execv=Erro encontrado ao chamar Java Web Start (execv)
error.launch.sysexec=Erro encontrado ao chamar Java Web Start (SysExec) 
error.listener.failed=Tela Inicial: falha em sysCreateListenerSocket
error.accept.failed=Tela Inicial: falha na fun\u00E7\u00E3o accept
error.recv.failed=Tela Inicial: falha na fun\u00E7\u00E3o recv
error.invalid.port=Tela Inicial: n\u00E3o reativou uma porta v\u00E1lida
error.read=Ler ap\u00F3s o final do buffer
error.xmlparsing=Erro durante o parsing de XML: tipo incorreto de token encontrado
error.splash.exit=Saindo do processamento da tela inicial do Java Web .....\n
# "Last WinSock Error" means the error message for the last operation that failed.
error.winsock=\t\u00DAltimo Erro de WinSock: 
error.winsock.load=N\u00E3o foi poss\u00EDvel carregar winsock.dll
error.winsock.start=Falha em WSAStartup
error.badinst.nohome=Instala\u00E7\u00E3o incorreta: JAVAWS_HOME n\u00E3o definido 
error.splash.noimage=Tela Inicial: n\u00E3o foi poss\u00EDvel carregar a imagem da tela inicial
error.splash.socket=Tela Inicial: falha no soquete do servidor
error.splash.cmnd=Tela Inicial: comando n\u00E3o reconhecido
error.splash.port=Tela Inicial: porta n\u00E3o especificada
error.splash.send=Tela Inicial: falha na fun\u00E7\u00E3o send
error.splash.timer=Tela Inicial: n\u00E3o foi poss\u00EDvel criar temporizador de shutdown
error.splash.x11.open=Tela Inicial: N\u00E3o \u00E9 poss\u00EDvel abrir a exibi\u00E7\u00E3o X11
error.splash.x11.connect=Tela Inicial: falha na conex\u00E3o X11
# Javaws usage: '\' is a joining of two sentence,which are connected including
# the invisible character '\n'.
message.javaws.usage=\nUso:\tjavaws [run-options] <jnlp-file>\t\n\tjavaws [control-options]\t\t\n\nem que run-options inclui:\t\t\t\n-verbose       \texibe a sa\u00EDda adicional\t\n-offline       \texecuta o aplicativo no modo off-line\t\n-system        \texecuta o aplicativo somente pelo cache do sistema\n-Xnosplash     \texecuta sem mostrar uma tela de abertura\t\n-J<option>     \tenvia a op\u00E7\u00E3o \u00E0 vm\t\n-wait          \tinicia o processo java e aguarda sua sa\u00EDda\t\n\ncontrol-options inclui:\t\n-viewer        \tmostra o visualizador do cache no painel de controle java\n-clearcache    \tremove do cache todos os aplicativos n\u00E3o instalados\n-uninstall     \tremove do cache todos os aplicativos\n-uninstall <jnlp-file>              \tremove o aplicativo do cache\t\n-import [import-options] <jnlp-file>\timporta o aplicativo para o cache\t\t\n\nimport-options inclui:\t\t\t\t\t\t\n-silent        \timporta silenciosamente (sem interface do usu\u00E1rio)\t\n-system        \timporta o aplicativo para o cache do sistema\t\n-codebase <url>\trecupera recursos da base de c\u00F3digo especificada\t\n-shortcut      \tinstala atalhos como se fosse um prompt permitido pelo usu\u00E1rio\t\n-association   \tinstala associa\u00E7\u00F5es como se fosse um prompt permitido pelo usu\u00E1rio\t\n\n
