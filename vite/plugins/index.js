import vue from "@vitejs/plugin-vue"
import vueJsx from "@vitejs/plugin-vue-jsx"
import Icons from "unplugin-icons/vite"
import creatStyleImport from "./style-import"
import createInspector from "./inspector"
import createComponents from "./components"
import createAutoImport from "./auto-import"
import createSvgIcon from "./svg-icon"
import createCompression from "./compression"
import createSetupExtend from "./setup-extend"
import { nodePolyfills } from "vite-plugin-node-polyfills"
//
export default function createVitePlugins(viteEnv, isBuild = false) {
  const vitePlugins = [
    vue(),
    vueJsx(),
    // iconify图标库
    Icons({
      // experimental
      autoInstall: true,
    }),
    nodePolyfills(),
  ]
  !isBuild && vitePlugins.push(createInspector())
  vitePlugins.push(creatStyleImport())
  vitePlugins.push(createComponents())
  vitePlugins.push(createAutoImport())
  vitePlugins.push(createSetupExtend())
  vitePlugins.push(createSvgIcon(isBuild))
  isBuild && vitePlugins.push(...createCompression(viteEnv))
  return vitePlugins
}
