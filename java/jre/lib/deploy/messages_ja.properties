#
# Copyright (c) 2004, 2013, Oracle and/or its affiliates. All rights reserved.
# ORACLE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
#

error.internal.badmsg=\u5185\u90E8\u30A8\u30E9\u30FC\u3001\u4E0D\u660E\u306A\u30E1\u30C3\u30BB\u30FC\u30B8
error.badinst.nojre=\u30A4\u30F3\u30B9\u30C8\u30FC\u30EB\u304C\u6B63\u3057\u304F\u3042\u308A\u307E\u305B\u3093\u3002\u69CB\u6210\u30D5\u30A1\u30A4\u30EB\u5185\u306BJRE\u304C\u3042\u308A\u307E\u305B\u3093
error.launch.execv=Java Web Start\u306E\u547C\u51FA\u3057\u4E2D\u306B\u30A8\u30E9\u30FC\u304C\u767A\u751F\u3057\u307E\u3057\u305F(execv)
error.launch.sysexec=Java Web Start\u306E\u547C\u51FA\u3057\u4E2D\u306B\u30A8\u30E9\u30FC\u304C\u767A\u751F\u3057\u307E\u3057\u305F(SysExec) 
error.listener.failed=\u30B9\u30D7\u30E9\u30C3\u30B7\u30E5: sysCreateListenerSocket\u306B\u5931\u6557\u3057\u307E\u3057\u305F
error.accept.failed=\u30B9\u30D7\u30E9\u30C3\u30B7\u30E5: accept\u306B\u5931\u6557\u3057\u307E\u3057\u305F
error.recv.failed=\u30B9\u30D7\u30E9\u30C3\u30B7\u30E5: recv\u306B\u5931\u6557\u3057\u307E\u3057\u305F
error.invalid.port=\u30B9\u30D7\u30E9\u30C3\u30B7\u30E5: \u6709\u52B9\u306A\u30DD\u30FC\u30C8\u3092\u5FA9\u6D3B\u3055\u305B\u308B\u3053\u3068\u304C\u3067\u304D\u307E\u305B\u3093\u3067\u3057\u305F
error.read=\u524D\u306E\u30D0\u30C3\u30D5\u30A1\u306E\u7D42\u308F\u308A\u3092\u8AAD\u307F\u8FBC\u307F\u307E\u3057\u305F
error.xmlparsing=XML\u89E3\u6790\u30A8\u30E9\u30FC: \u8AA4\u3063\u305F\u30C8\u30FC\u30AF\u30F3\u304C\u691C\u51FA\u3055\u308C\u307E\u3057\u305F
error.splash.exit=Java Web Start\u30B9\u30D7\u30E9\u30C3\u30B7\u30E5\u753B\u9762\u51E6\u7406\u3092\u7D42\u4E86\u3057\u307E\u3059.....\n
# "Last WinSock Error" means the error message for the last operation that failed.
error.winsock=\t\u6700\u5F8C\u306EWinSock\u30A8\u30E9\u30FC: 
error.winsock.load=winsock.dll\u3092\u30ED\u30FC\u30C9\u3067\u304D\u307E\u305B\u3093
error.winsock.start=WSAStartup\u306B\u5931\u6557\u3057\u307E\u3057\u305F
error.badinst.nohome=\u30A4\u30F3\u30B9\u30C8\u30FC\u30EB\u304C\u6B63\u3057\u304F\u3042\u308A\u307E\u305B\u3093: JAVAWS_HOME\u304C\u8A2D\u5B9A\u3055\u308C\u3066\u3044\u307E\u305B\u3093 
error.splash.noimage=\u30B9\u30D7\u30E9\u30C3\u30B7\u30E5: \u30B9\u30D7\u30E9\u30C3\u30B7\u30E5\u753B\u9762\u306E\u753B\u50CF\u3092\u30ED\u30FC\u30C9\u3067\u304D\u307E\u305B\u3093
error.splash.socket=\u30B9\u30D7\u30E9\u30C3\u30B7\u30E5: \u30B5\u30FC\u30D0\u30FC\u30FB\u30BD\u30B1\u30C3\u30C8\u306B\u969C\u5BB3\u304C\u767A\u751F\u3057\u307E\u3057\u305F
error.splash.cmnd=\u30B9\u30D7\u30E9\u30C3\u30B7\u30E5: \u8A8D\u8B58\u3055\u308C\u306A\u3044\u30B3\u30DE\u30F3\u30C9
error.splash.port=\u30B9\u30D7\u30E9\u30C3\u30B7\u30E5: \u30DD\u30FC\u30C8\u304C\u6307\u5B9A\u3055\u308C\u3066\u3044\u307E\u305B\u3093
error.splash.send=\u30B9\u30D7\u30E9\u30C3\u30B7\u30E5: \u9001\u4FE1\u306B\u5931\u6557\u3057\u307E\u3057\u305F
error.splash.timer=\u30B9\u30D7\u30E9\u30C3\u30B7\u30E5: \u30B7\u30E3\u30C3\u30C8\u30C0\u30A6\u30F3\u30FB\u30BF\u30A4\u30DE\u30FC\u3092\u4F5C\u6210\u3067\u304D\u307E\u305B\u3093
error.splash.x11.open=\u30B9\u30D7\u30E9\u30C3\u30B7\u30E5: X11\u30C7\u30A3\u30B9\u30D7\u30EC\u30A4\u3092\u958B\u3051\u307E\u305B\u3093
error.splash.x11.connect=\u30B9\u30D7\u30E9\u30C3\u30B7\u30E5: X11\u63A5\u7D9A\u306B\u5931\u6557\u3057\u307E\u3057\u305F
# Javaws usage: '\' is a joining of two sentence,which are connected including
# the invisible character '\n'.
message.javaws.usage=\n\u4F7F\u7528\u65B9\u6CD5:\tjavaws [run-options] <jnlp-file>\t\n\tjavaws [control-options]\t\t\n\nrun-options\u306B\u306F\u6B21\u306E\u3082\u306E\u304C\u3042\u308A\u307E\u3059\u3002\t\t\t\n-verbose       \t\u8FFD\u52A0\u306E\u51FA\u529B\u3092\u8868\u793A\t\n-offline       \t\u30A2\u30D7\u30EA\u30B1\u30FC\u30B7\u30E7\u30F3\u3092\u30AA\u30D5\u30E9\u30A4\u30F3\u30FB\u30E2\u30FC\u30C9\u3067\u5B9F\u884C\t\n-system        \t\u30A2\u30D7\u30EA\u30B1\u30FC\u30B7\u30E7\u30F3\u3092\u30B7\u30B9\u30C6\u30E0\u30FB\u30AD\u30E3\u30C3\u30B7\u30E5\u306E\u307F\u304B\u3089\u5B9F\u884C\n-Xnosplash     \t\u30B9\u30D7\u30E9\u30C3\u30B7\u30E5\u753B\u9762\u3092\u8868\u793A\u305B\u305A\u306B\u5B9F\u884C\t\n-J<option>     \t\u30AA\u30D7\u30B7\u30E7\u30F3\u3092VM\u306B\u4E0E\u3048\u308B\t\n-wait          \tJava\u30D7\u30ED\u30BB\u30B9\u3092\u958B\u59CB\u3057\u3001\u305D\u306E\u7D42\u4E86\u3092\u5F85\u6A5F\t\n\ncontrol-options\u306B\u306F\u6B21\u306E\u3082\u306E\u304C\u3042\u308A\u307E\u3059\u3002\t\n-viewer        \t\u30AD\u30E3\u30C3\u30B7\u30E5\u30FB\u30D3\u30E5\u30FC\u30A2\u3092Java\u30B3\u30F3\u30C8\u30ED\u30FC\u30EB\u30FB\u30D1\u30CD\u30EB\u306B\u8868\u793A\n-clearcache    \t\u672A\u30A4\u30F3\u30B9\u30C8\u30FC\u30EB\u306E\u3059\u3079\u3066\u306E\u30A2\u30D7\u30EA\u30B1\u30FC\u30B7\u30E7\u30F3\u3092\u30AD\u30E3\u30C3\u30B7\u30E5\u304B\u3089\u524A\u9664\n-uninstall     \t\u3059\u3079\u3066\u306E\u30A2\u30D7\u30EA\u30B1\u30FC\u30B7\u30E7\u30F3\u3092\u30AD\u30E3\u30C3\u30B7\u30E5\u304B\u3089\u524A\u9664\n-uninstall <jnlp-file>              \t\u30A2\u30D7\u30EA\u30B1\u30FC\u30B7\u30E7\u30F3\u3092\u30AD\u30E3\u30C3\u30B7\u30E5\u304B\u3089\u524A\u9664\t\n-import [import-options] <jnlp-file>\t\u30A2\u30D7\u30EA\u30B1\u30FC\u30B7\u30E7\u30F3\u3092\u30AD\u30E3\u30C3\u30B7\u30E5\u306B\u30A4\u30F3\u30DD\u30FC\u30C8\t\t\n\nimport-options\u306B\u306F\u6B21\u306E\u3082\u306E\u304C\u3042\u308A\u307E\u3059\u3002\t\n-silent        \t\u30E1\u30C3\u30BB\u30FC\u30B8\u3092\u8868\u793A\u305B\u305A\u306B\u30A4\u30F3\u30DD\u30FC\u30C8(\u30E6\u30FC\u30B6\u30FC\u30FB\u30A4\u30F3\u30BF\u30D5\u30A7\u30FC\u30B9\u306A\u3057)\t\n-system        \t\u30A2\u30D7\u30EA\u30B1\u30FC\u30B7\u30E7\u30F3\u3092\u30B7\u30B9\u30C6\u30E0\u30FB\u30AD\u30E3\u30C3\u30B7\u30E5\u306B\u30A4\u30F3\u30DD\u30FC\u30C8\t\n-codebase <url>\t\u6307\u5B9A\u3055\u308C\u305F\u30B3\u30FC\u30C9\u30FB\u30D9\u30FC\u30B9\u304B\u3089\u30EA\u30BD\u30FC\u30B9\u3092\u53D6\u5F97\t\n-shortcut      \t\u30E6\u30FC\u30B6\u30FC\u304C\u30D7\u30ED\u30F3\u30D7\u30C8\u3092\u53D7\u3051\u5165\u308C\u305F\u3082\u306E\u3068\u3057\u3066\u30B7\u30E7\u30FC\u30C8\u30AB\u30C3\u30C8\u3092\u30A4\u30F3\u30B9\u30C8\u30FC\u30EB\t\n-association   \t\u30E6\u30FC\u30B6\u30FC\u304C\u30D7\u30ED\u30F3\u30D7\u30C8\u3092\u53D7\u3051\u5165\u308C\u305F\u3082\u306E\u3068\u3057\u3066\u30A2\u30BD\u30B7\u30A8\u30FC\u30B7\u30E7\u30F3\u3092\u30A4\u30F3\u30B9\u30C8\u30FC\u30EB\t\n\n
