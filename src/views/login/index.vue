<template>
  <Frame class="fixed z-9999" :buttons="['minimize', 'close']" />
  <div
    class="w-full h-full p-10"
    :style="{
      backgroundImage: `linear-gradient(
            to right,
            rgba(2, 133, 165, 0.8),
            rgba(79, 172, 254, 0.8)
          ), url(${bgImage})`
    }">
    <div class="text-center mb-5">
      <h2 class="text-xl font-semibold mb-6 tracking-wide text-white">
        {{ systemName }}
      </h2>
    </div>
    <!-- 登录表单 -->
    <el-form v-if="activeForm === 'login'" ref="loginRef" :model="loginForm" :rules="loginRules" class="space-y-6" label-position="top">
      <el-form-item prop="userId" label="账号" class="mb-6">
        <el-input v-model="loginForm.userId" type="text" size="default" placeholder="请输入账号" class="no-autofill">
          <template #prefix>
            <el-icon><User /></el-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="password" label="密码" class="mb-6">
        <el-input v-model="loginForm.password" type="password" size="default" placeholder="请输入密码" @keyup.enter="handleLogin" class="no-autofill" show-password>
          <template #prefix>
            <el-icon><Lock /></el-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item class="w-full">
        <el-button
          :loading="loginLoading"
          size="large"
          type="primary"
          class="w-full bg-gradient-to-r from-[#0285a5] to-[#4facfe] text-white font-semibold py-4 rounded-lg shadow-md"
          @click="handleLogin">
          <span v-if="!loginLoading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 重置密码表单 -->
    <el-form v-if="activeForm === 'reset'" ref="resetRef" :model="resetForm" :rules="resetRules" class="space-y-6" label-position="top">
      <el-form-item prop="userId" label="用户ID">
        <el-input v-model="resetForm.userId" size="default" placeholder="请输入用户ID" autocomplete="new-password" class="no-autofill">
          <template #prefix>
            <el-icon><User /></el-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="email" label="邮箱">
        <el-input v-model="resetForm.email" type="email" size="default" placeholder="请输入注册邮箱" autocomplete="new-password" class="no-autofill">
          <template #prefix>
            <el-icon><Message /></el-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="phone" label="联系方式">
        <el-input v-model="resetForm.phone" autocomplete="new-password" class="no-autofill" size="default" placeholder="请输入联系方式">
          <template #prefix>
            <el-icon><Phone /></el-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="verifyCode" label="验证码" class="flex">
        <div class="flex gap-5 items-start flex-1">
          <el-input v-model="resetForm.verifyCode" size="default" placeholder="请输入验证码" class="flex-grow no-autofill" autocomplete="new-password">
            <template #prefix>
              <el-icon><Picture /></el-icon>
            </template>
          </el-input>
          <Captcha :reqId="resetForm.reqId" :width="128" :height="48" @update:reqId="reqId => (resetForm.reqId = reqId)" ref="resetCaptchaRef" />
        </div>
      </el-form-item>
      <el-form-item prop="newPassword" label="新密码">
        <el-input v-model="resetForm.newPassword" type="password" size="default" placeholder="请输入新密码" show-password class="flex-grow no-autofill" autocomplete="new-password">
          <template #prefix>
            <el-icon><Lock /></el-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="confirmPassword" label="确认密码">
        <el-input v-model="resetForm.confirmPassword" type="password" size="default" placeholder="请再次输入新密码" show-password>
          <template #prefix>
            <el-icon><Lock /></el-icon>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import Cookies from 'js-cookie'
import { useUserStore } from '@/store'
import { User, Lock, Picture, Message, Phone } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import Captcha from '@/components/Captcha/index.vue'
import bgImage from '@/assets/images/login_back.jpg'
import Frame from '@/components/frame/index.vue'

const userStore = useUserStore()
const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()
const systemName = import.meta.env.VITE_SYSTEM_NAME
if (Cookies.get('lang')) Cookies.set('lang', 'zh')

// 表单切换控制
const activeForm = ref('login')

const loginForm = ref({
  userId: '',
  password: '',
  verifyCode: '',
  reqId: ''
})

const loginRules = {
  userId: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
  password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
  verifyCode: [{ required: true, trigger: 'change', message: '请输入验证码' }]
}

// 重置密码表单
const resetForm = ref({
  userId: '',
  email: '',
  phone: '',
  verifyCode: '',
  newPassword: '',
  confirmPassword: '',
  reqId: ''
})

// 重置密码表单验证规则
const resetRules = {
  userId: [{ required: true, trigger: 'blur', message: '请输入用户ID' }],
  email: [
    { required: true, trigger: 'blur', message: '请输入邮箱' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [{ required: true, trigger: 'blur', message: '请输入联系方式' }],
  verifyCode: [{ required: true, trigger: 'change', message: '请输入验证码' }],
  newPassword: [{ required: true, trigger: 'blur', message: '请输入新密码' }],
  confirmPassword: [
    { required: true, trigger: 'blur', message: '请再次输入新密码' },
    { validator: validateResetPassword, trigger: 'blur' }
  ]
}

const loading = ref(false)
const resetLoading = ref(false)
const registerLoading = ref(false)

// 注册开关
const redirect = ref(undefined)

// 注册表单数据
const registerForm = ref({
  userId: '',
  userName: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  reqId: ''
})

// 注册表单验证规则
const registerRules = {
  userId: [{ required: true, trigger: 'blur', message: '请输入用户名' }],
  userName: [{ required: true, trigger: 'blur', message: '请输入昵称' }],
  email: [
    { required: true, trigger: 'blur', message: '请输入邮箱' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [{ required: true, trigger: 'blur', message: '请输入联系方式' }],
  password: [{ required: true, trigger: 'blur', message: '请输入密码' }],
  confirmPassword: [
    { required: true, trigger: 'blur', message: '请再次输入密码' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

watch(
  route,
  newRoute => {
    redirect.value = newRoute.query && newRoute.query.redirect
  },
  { immediate: true }
)
const loginRef = ref(null)
const loginLoading = ref(false)
function handleLogin() {
  loginRef.value.validate(async valid => {
    if (valid) {
      loginLoading.value = true
      try {
        // 使用Store进行登录
        await userStore.login(loginForm.value)

        // 获取用户信息
        await userStore.getInfo()
        // 记住密码处理
        // Cookies.set("userId", loginForm.value.userId, { expires: 30 });
        // Cookies.set("password", encrypt(loginForm.value.password), {
        //   expires: 30,
        // });
        // 登录成功提示
        ElMessage({
          message: '登录成功',
          type: 'success'
        })

        // 获取重定向地址
        const redirect = route.query.redirect || '/'
        // 跳转到重定向地址
        router.push(redirect)
      } catch (error) {
        console.error('登录失败:', error)
      } finally {
        loginLoading.value = false
      }
    }
  })
}

// 重置密码确认密码验证
function validateResetPassword(rule, value, callback) {
  if (value !== resetForm.value.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 确认密码验证函数
function validateConfirmPassword(rule, value, callback) {
  if (value !== registerForm.value.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}
// 登录失败时刷新验
const resetCaptchaRef = ref(null)
</script>
<style scoped lang="scss">
::v-deep .el-form-item__label {
  color: #fff;
}
</style>
<style>
.no-autofill :deep(input) {
  background-color: transparent !important;
  font-size: 0.95rem !important;
  padding: 0.75rem 0 !important;
}

.no-autofill :deep(input:-webkit-autofill),
.no-autofill :deep(input:-webkit-autofill:hover),
.no-autofill :deep(input:-webkit-autofill:focus),
.no-autofill :deep(input:-webkit-autofill:active) {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
  -webkit-text-fill-color: inherit !important;
  caret-color: inherit !important;
  transition: background-color 99999s ease-out !important;
}

.no-autofill :deep(.el-input__wrapper) {
  background-color: transparent !important;
  box-shadow: 0 0 0 1px rgba(2, 133, 165, 0.3) inset !important;
  border-radius: 0.75rem !important;
  padding: 0.25rem 0.75rem !important;
  height: 3rem !important;
  transition: all 0.3s ease !important;
}

.no-autofill :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(2, 133, 165, 0.4) inset !important;
  background-color: rgba(2, 133, 165, 0.02) !important;
}

.no-autofill :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px rgba(2, 133, 165, 0.5) inset !important;
}

/* 输入框图标样式 */
.no-autofill :deep(.el-input__prefix) {
  padding: 0 0.5rem !important;
  font-size: 1.1rem !important;
  color: rgba(2, 133, 165, 0.6) !important;
}

.no-autofill :deep(.el-input__prefix-inner) {
  display: flex !important;
  align-items: center !important;
}

/* 表单项样式优化 */
:deep(.el-form-item) {
  margin-bottom: 1.5rem !important;
}

/* 表单标签样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500 !important;
  color: #0285a5 !important;
  opacity: 0.8;
  padding-bottom: 0.5rem !important;
  font-size: 0.95rem !important;
}

/* 验证码组件样式优化 */
:deep(.captcha-container) {
  border-radius: 0.75rem !important;
  overflow: hidden !important;
  box-shadow: 0 2px 4px rgba(2, 133, 165, 0.1) !important;
}

/* 按钮样式优化 */
:deep(.el-button--primary) {
  background: linear-gradient(135deg, #0285a5, #4facfe) !important;
  border: none !important;
  height: 3rem !important;
  border-radius: 0.75rem !important;
  box-shadow:
    0 4px 6px -1px rgba(2, 133, 165, 0.1),
    0 2px 4px -1px rgba(2, 133, 165, 0.06) !important;
  opacity: 0.9;
  transition: all 0.3s ease !important;
}

:deep(.el-button--primary:hover) {
  opacity: 1;
  box-shadow:
    0 10px 15px -3px rgba(2, 133, 165, 0.1),
    0 4px 6px -2px rgba(2, 133, 165, 0.05) !important;
}

/* 添加全局禁用自动填充样式 */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  transition: background-color 99999s ease-out !important;
  -webkit-box-shadow: 0 0 0 30px white inset !important;
}

/* 密码框小眼睛图标样式 */
.no-autofill :deep(.el-input__suffix) {
  padding: 0 0.5rem !important;
  color: rgba(2, 133, 165, 0.6) !important;
}

.no-autofill :deep(.el-input__suffix-inner) {
  display: flex !important;
  align-items: center !important;
}
</style>
