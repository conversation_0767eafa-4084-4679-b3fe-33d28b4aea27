<template>
  <Frame class="fixed z-9999 bg-sky-500" :buttons="['minimize', 'close']" />
  <div class="p-10 w-full h-full flex items-center justify-center flex-col">
    <!-- 个人信息表单 -->
    <!-- 表单内容 -->
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" class="personal-info-form w-full" label-position="left">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- 用户名 -->
        <el-form-item label="用户名" prop="userName">
          <el-input v-model="formData.userName" placeholder="请输入新用户名" clearable :prefix-icon="User" class="w-full" />
        </el-form-item>

        <!-- 所属组织 -->
        <el-form-item label="所属组织" prop="comGroup">
          <el-input v-model="formData.comGroup" placeholder="请输入所属组织" clearable :prefix-icon="OfficeBuilding" class="w-full" :disabled="!comGroupEditable" />
        </el-form-item>

        <!-- 新密码 -->
        <el-form-item label="新密码" prop="password">
          <el-input v-model="formData.password" type="password" placeholder="请输入新密码" clearable show-password :prefix-icon="Lock" class="w-full" />
        </el-form-item>

        <!-- 确认密码 -->
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="formData.confirmPassword" type="password" placeholder="请再次输入新密码" clearable show-password :prefix-icon="Lock" class="w-full" />
        </el-form-item>
      </div>
    </el-form>
    <div class="flex justify-center space-x-4">
      <el-button type="primary" :loading="loading" @click="handleSubmit" class="px-8 py-2" size="small">
        <template #icon>
          <el-icon><Check /></el-icon>
        </template>
        保存修改
      </el-button>
      <el-button @click="handleReset" class="px-8 py-2" size="small">
        <template #icon>
          <el-icon><Refresh /></el-icon>
        </template>
        重置
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Lock, OfficeBuilding, Check, Refresh, InfoFilled } from '@element-plus/icons-vue'
import { useUserStore } from '@/store'
import Apis from '@/apis'
import Frame from '@/components/frame/index.vue'

// 用户信息存储
const userStore = useUserStore()
const comGroupEditable = ref(!userStore.comGroup)
// 表单引用
const formRef = ref()

// 加载状态
const loading = ref(false)

// 表单数据
const formData = reactive({
  userName: '',
  comGroup: '',
  password: '',
  confirmPassword: ''
})

// 表单验证规则
const rules = reactive({
  userName: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度为2-20个字符', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/,
      message: '用户名只能包含字母、数字、下划线和中文',
      trigger: 'blur'
    }
  ],
  comGroup: [
    { required: true, message: '请输入所属组织', trigger: 'blur' },
    { min: 2, max: 50, message: '组织名称长度为2-50个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, message: '密码长度至少8位', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value === formData.userName) {
          callback(new Error('密码不能与用户名相同'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== formData.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
})

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  formRef.value.validate(async valid => {
    if (valid) {
      //     // 调用API
      const response = await Apis.general.post_shipstruct_user_changeuserinfo({
        data: {
          userName: formData.userName,
          comGroup: formData.comGroup,
          password: formData.password
        }
      })
      if (response.code === 200) {
        ElMessage.success('个人信息修改成功')
      } else {
        ElMessage.error(response.msg || '修改失败，请重试')
      }
    } else {
      console.log(error)
    }
  })
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  formData.userName = ''
  formData.comGroup = userStore.comGroup || ''
  formData.password = ''
  formData.confirmPassword = ''
}

// 初始化数据
const initData = () => {
  // 这里可以从用户store中获取当前用户信息
  formData.userName = userStore.userName || ''
  formData.comGroup = userStore.comGroup || ''
}

onMounted(() => {
  initData()
})
</script>

<style scoped>
.personal-info-form {
  max-width: 100%;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-button) {
  border-radius: 6px;
}

@media (max-width: 768px) {
  .personal-info-form {
    padding: 0;
  }
}
</style>
