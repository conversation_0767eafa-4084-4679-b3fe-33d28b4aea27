<template>
  <div class="p-5">
    <div class="max-w-4xl mx-auto">
      <!-- 申请表单 -->
      <el-form ref="formRef" :model="applicationForm" :rules="formRules" label-width="120px">
        <div class="grid grid-cols-1 md:grid-cols-1 gap-6">
          <el-form-item label="收件人邮箱" prop="toAccount" required>
            <el-input v-model="applicationForm.toAccount" placeholder="请输入收件人邮箱地址" clearable />
          </el-form-item>

          <el-form-item label="邮件主题" prop="subject" required>
            <el-input v-model="applicationForm.subject" placeholder="请输入邮件主题" clearable />
          </el-form-item>

          <el-form-item label="邮件内容" prop="content" required>
            <el-input v-model="applicationForm.content" placeholder="请输入邮件内容" clearable type="textarea" :rows="4" />
          </el-form-item>
        </div>
      </el-form>
      <div class="flex justify-center space-x-4">
        <el-button size="large" @click="handleReset">重置申请</el-button>
        <el-button type="primary" size="large" :loading="submitting" @click="handleSubmit">
          {{ submitting ? '提交中...' : '提交申请' }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import Apis from '@/apis'

// 表单引用
const formRef = ref(null)

// 申请表单数据
const applicationForm = reactive({
  toAccount: '',
  subject: '',
  content: ''
})

// 提交状态
const submitting = ref(false)

// 表单验证规则
const formRules = {
  toAccount: [
    { required: true, message: '请输入收件人邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '请输入邮件主题', trigger: 'blur' },
    {
      min: 2,
      max: 100,
      message: '主题长度在 2 到 100 个字符',
      trigger: 'blur'
    }
  ],
  content: [
    { required: true, message: '请输入邮件内容', trigger: 'blur' },
    {
      min: 10,
      max: 1000,
      message: '内容长度在 10 到 1000 个字符',
      trigger: 'blur'
    }
  ]
}

// 重置申请
const handleReset = () => {
  ElMessageBox.confirm('确定要重置申请信息吗？所有填写的内容将被清空。', '重置确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      formRef.value?.resetFields()
      ElMessage.success('申请信息已重置')
    })
    .catch(() => {
      // 取消重置
    })
}

// 提交申请
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    // 验证表单
    await formRef.value.validate()

    submitting.value = true

    // 调用API提交申请
    const response = await Apis.general.post_shipstruct_ship_apply({
      data: {
        toAccount: applicationForm.toAccount,
        subject: applicationForm.subject,
        content: applicationForm.content
      }
    })

    console.log('申请提交响应:', response)

    ElMessage.success('申请提交成功！我们将在3个工作日内处理您的申请并发送至指定邮箱。')

    // 重置表单
    setTimeout(() => {
      formRef.value?.resetFields()
    }, 1000)
  } catch (error) {
    console.error('申请提交失败:', error)
    ElMessage.error('申请提交失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
:deep(.el-form-item) {
  margin-bottom: 1.5rem;
}
</style>
