:deep(.el-form-item) {
    margin-bottom: 1.5rem;
}

:deep(.el-upload-dragger) {
    border: 2px dashed #d1d5db;
    border-radius: 0.75rem;
    background: #f9fafb;
    transition: all 0.3s ease;
}

:deep(.el-upload-dragger:hover) {
    border-color: #3b82f6;
    background: #eff6ff;
}

:deep(.el-input-number) {
    width: 100%;
}

:deep(.el-input-number .el-input__inner) {
    text-align: left;
}

.space-y-6>*+* {
    margin-top: 1.5rem;
}

::v-deep .el-form-item__content {
    align-items: flex-start;
}

::v-deep .el-upload-dragger {
    height: 270px;
}