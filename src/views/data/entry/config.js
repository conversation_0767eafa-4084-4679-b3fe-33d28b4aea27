export const basicRulesObj = {
  shipName: [
    { required: true, message: '请输入船名', trigger: 'blur' },
    { min: 2, max: 50, message: '船名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  shipOwner: [
    { required: true, message: '请输入船东名称', trigger: 'blur' },
    { min: 2, max: 100, message: '船东名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  shipType: [{ required: true, message: '请选择船舶类型', trigger: 'change' }],
  area: [{ required: true, message: '请选择航区', trigger: 'change' }]
  // shipYard: [
  //     { required: true, message: "请输入船厂", trigger: "blur" },
  //     { min: 2, max: 100, message: "船厂长度在 2 到 100 个字符", trigger: "blur" },
  // ],
  // imo: [
  //     { required: true, message: "请输入IMO编号", trigger: "blur" },
  // ],
}

export const basicInfoObj = {
  shipName: '',
  shipOwner: '',
  shipYard: '',
  imo: '',
  keelDate: '',
  dwt: '',
  lsw: '',
  ndw: '',
  shipLoa: '',
  shipLpp: '',
  shipLrule: '',
  moldWidth: '',
  moldDepth: '',
  structDraft: '',
  designDraft: '',
  beamArch: '',
  dySwbmHogg: '',
  dySwbmSagg: '',
  staSwbmHogg: '',
  staSwbmSagg: '',
  csa: '',
  csm: '',
  shipType: '',
  area: '',
  csaPrefix: '',
  csaInput: '',
  csmPrefix: '',
  csmInput: '',
  // 结构特征
  features: {
    纵舱壁: '',
    横撑: '',
    边舱: '',
    船底骨架: '',
    舷侧骨架: '',
    甲板骨架: '',
    舱壁: '',
    舱壁结构: '',
    底凳: '',
    顶凳: '',
    球鼻艏: '',
    船型: '',
    上建: [],
    端部加强: [],
    设备: [],
    装载: [],
    自动控制: [],
    特殊检验: [],
    货物冷藏: [],
    其他: []
  }
}
export const loadInfoObj = {
  // 装载信息
  载重吨: null,
  总吨位: null,
  净吨位: null,
  货舱容积: null,
  最大装载量: null,
  燃油容量: null,
  装载备注: null
}
export const drawingInfoObj = {
  type: '',
  subtitle: ''
}
