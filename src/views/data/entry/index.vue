<template>
  <div class="p-5">
    <div class="max-w-7xl mx-auto">
      <el-form ref="formRef" :model="basicInfo" :rules="basicRules" label-width="120px" class="space-y-6">
        <!-- 第一行：基本信息 -->
        <div class="mb-6">
          <h2 class="text-xl font-semibold text-gray-800 flex items-center">
            <el-icon class="mr-2 text-green-500"><Ship /></el-icon>
            船舶信息
          </h2>
          <!-- 第一行：基本信息 -->
          <div class="border-b border-gray-200 flex items-start mb-1 flex-col">
            <div class="text-lg font-semibold text-gray-800 flex items-center w-[100px] flex-shrink-0 m-0 mb-2">
              <span class="w-1 h-6 bg-blue-500 mr-3"></span>
              基本信息
            </div>

            <!-- 折叠按钮 -->
            <div class="flex-1 w-full">
              <div class="grid grid-cols-3 relative auto-cols-fr gap-x-4">
                <el-form-item label="船名" class="mb-0" prop="shipName">
                  <el-input v-model="basicInfo.shipName" placeholder="请输入船名" clearable class="w-full">
                    <template #prefix>
                      <el-icon><Search /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item label="船东" class="mb-0" prop="shipOwner">
                  <el-input v-model="basicInfo.shipOwner" placeholder="请输入船东名称" clearable class="w-full" />
                </el-form-item>
                <!-- 船舶类型 -->
                <el-form-item label="船舶类型" class="mb-0 w-full" prop="shipType">
                  <el-select v-model="basicInfo.shipType" placeholder="请选择船舶类型" clearable class="w-full">
                    <el-option label="普通干货船" value="普通干货船" />
                    <el-option label="甲板货船" value="甲板货船" />
                    <el-option label="冷藏货船" value="冷藏货船" />
                    <el-option label="滚装船" value="滚装船" />
                    <el-option label="散货船" value="散货船" />
                    <el-option label="油船" value="油船" />
                    <el-option label="矿/油船" value="矿/油船" />
                    <el-option label="矿/散/油船" value="矿/散/油船" />
                    <el-option label="集装箱船" value="集装箱船" />
                    <el-option label="敞口集装箱船" value="敞口集装箱船" />
                    <el-option label="油驳" value="油驳" />
                    <el-option label="化学品驳" value="化学品驳" />
                    <el-option label="气体运输驳" value="气体运输驳" />
                    <el-option label="箱形驳" value="箱形驳" />
                    <el-option label="化学品液货船" value="化学品液货船" />
                    <el-option label="液化气体船" value="液化气体船" />
                    <el-option label="LNG运输船" value="LNG运输船" />
                    <el-option label="CNG运输船" value="CNG运输船" />
                    <el-option label="化学品/油液货船" value="化学品/油液货船" />
                    <el-option label="极地破冰船" value="极地破冰船" />
                    <el-option label="加强型破冰船" value="加强型破冰船" />
                    <el-option label="固定锚地储油船" value="固定锚地储油船" />
                    <el-option label="浮油回收船" value="浮油回收船" />
                  </el-select>
                </el-form-item>
              </div>
              <div class="grid grid-cols-3 relative auto-cols-fr gap-x-4">
                <!-- 航区 -->
                <el-form-item label="航区" class="mb-0" prop="area">
                  <el-select v-model="basicInfo.area" placeholder="请选择航区" clearable class="w-full">
                    <el-option label="1类航区" value="1类航区" />
                    <el-option label="2类航区" value="2类航区" />
                    <el-option label="3类航区" value="3类航区" />
                    <el-option label="非国际1类航区" value="非国际1类航区" />
                    <el-option label="非国际2类航区" value="非国际2类航区" />
                    <el-option label="非国际3类航区" value="非国际3类航区" />
                    <el-option label="特定航线" value="特定航线" />
                    <el-option label="特定航区" value="特定航区" />
                    <el-option label="特定航线船体结构强度评估" value="特定航线船体结构强度评估" />
                    <el-option label="远海营运限制" value="远海营运限制" />
                    <el-option label="近海营运限制" value="近海营运限制" />
                    <el-option label="沿海营运限制" value="沿海营运限制" />
                    <el-option label="遮蔽营运限制" value="遮蔽营运限制" />
                    <el-option label="平静水域营运限制" value="平静水域营运限制" />
                    <el-option label="营运气象" value="营运气象" />
                    <el-option label="在3类航区内作业" value="在3类航区内作业" />
                    <el-option label="在2类航区内作业" value="在2类航区内作业" />
                    <el-option label="在1类航区内作业" value="在1类航区内作业" />
                  </el-select>
                </el-form-item>
                <el-form-item label="船厂" class="mb-0" prop="shipYard">
                  <el-input v-model="basicInfo.shipYard" placeholder="请输入船厂" clearable class="w-full"></el-input>
                </el-form-item>
                <el-form-item label="IMO编号" class="mb-0" prop="imo">
                  <el-input v-model="basicInfo.imo" placeholder="请输入IMO编号" clearable class="w-full"></el-input>
                </el-form-item>
                <el-form-item label="排水量" class="mb-0" prop="dwt">
                  <el-input v-model="basicInfo.dwt" placeholder="请输入排水量(t)" clearable class="w-full" />
                </el-form-item>
                <el-form-item label="空船重量" class="mb-0" prop="lsw">
                  <el-input v-model="basicInfo.lsw" placeholder="请输入空船重量(t)" clearable class="w-full" />
                </el-form-item>
                <el-form-item label="净载重量" class="mb-0" prop="ndw">
                  <el-input v-model="basicInfo.ndw" placeholder="请输入净载重量(t)" clearable class="w-full" />
                </el-form-item>
                <el-form-item label="总长" class="mb-0" prop="shipLoa">
                  <el-input v-model="basicInfo.shipLoa" placeholder="请输入总长(m)" clearable class="w-full" />
                </el-form-item>

                <el-form-item label="垂线间长" class="mb-0" prop="shipLpp">
                  <el-input v-model="basicInfo.shipLpp" placeholder="请输入垂线间长(m)" clearable class="w-full" />
                </el-form-item>

                <el-form-item label="规范船长" class="mb-0" prop="shipLrule">
                  <el-input v-model="basicInfo.shipLrule" placeholder="请输入规范船长(m)" clearable class="w-full" />
                </el-form-item>

                <el-form-item label="型宽" class="mb-0" prop="moldWidth">
                  <el-input v-model="basicInfo.moldWidth" placeholder="请输入型宽(m)" clearable class="w-full" />
                </el-form-item>

                <el-form-item label="型深" class="mb-0" prop="moldDepth">
                  <el-input v-model="basicInfo.moldDepth" placeholder="请输入型深(m)" clearable class="w-full" />
                </el-form-item>
                <el-form-item label="梁拱" class="mb-0" prop="beamArch">
                  <el-input v-model="basicInfo.beamArch" placeholder="请输入梁拱(mm)" clearable class="w-full" />
                </el-form-item>
                <el-form-item label="结构吃水" class="mb-0" prop="structDraft">
                  <el-input v-model="basicInfo.structDraft" placeholder="请输入结构吃水(m)" clearable class="w-full" />
                </el-form-item>

                <el-form-item label="设计吃水" class="mb-0" prop="designDraft">
                  <el-input v-model="basicInfo.designDraft" placeholder="请输入设计吃水(m)" clearable class="w-full" />
                </el-form-item>
                <el-form-item label="铺龙骨日期" class="mb-0 keelDate-item" prop="keelDate">
                  <el-date-picker v-model="basicInfo.keelDate" type="date" placeholder="请选择铺龙骨日期" clearable class="!w-full" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
                </el-form-item>
                <div class="grid grid-cols-2 col-span-3 relative auto-cols-fr gap-x-4 mb-[24px] ml-[37px]">
                  <div class="border-solid border-1 border-slate-200 flex flex-row justify-center rounded-md p-4">
                    <div class="el-form-item__label !h-full !flex !justify-center !items-center !border-r-1 !border-slate-200 border-r-solid mr-4 text-[14px]">
                      <span>
                        航行工况
                        <br />
                        静水弯矩
                      </span>
                    </div>
                    <div class="flex flex-col justify-between w-full">
                      <el-form-item class="!mb-0 w-full" prop="dySwbmHogg" label="中拱" label-width="50px">
                        <!-- <template #label>
                          <el-tooltip content="航行工况静水弯矩-中拱(kN·m)" placement="top">
                            <span class="inline-block width-[120px] text-nowrap overflow-hidden text-ellipsis whitespace-nowrap">中拱</span>
                          </el-tooltip>
                        </template> -->
                        <el-input v-model="basicInfo.dySwbmHogg" placeholder="请输入航行工况静水弯矩-中拱(kN·m)" clearable class="w-full" />
                      </el-form-item>

                      <el-form-item class="!mb-0 w-full" prop="dySwbmSagg" label="中垂" label-width="50px">
                        <!-- <template #label>
                          <el-tooltip content="航行工况静水弯矩-中垂(kN·m)" placement="top">
                            <span class="inline-block width-[120px] text-nowrap overflow-hidden text-ellipsis whitespace-nowrap">中垂</span>
                          </el-tooltip>
                        </template> -->
                        <el-input v-model="basicInfo.dySwbmSagg" placeholder="请输入航行工况静水弯矩-中垂(kN·m)" clearable class="w-full" />
                      </el-form-item>
                    </div>
                  </div>
                  <div class="border-solid border-1 border-slate-200 flex flex-row justify-center rounded-md p-4">
                    <div class="el-form-item__label !h-full !flex !justify-center !items-center !border-r-1 !border-slate-200 border-r-solid mr-4 text-[14px]">
                      <span>
                        港内工况
                        <br />
                        静水弯矩
                      </span>
                    </div>
                    <div class="flex flex-col justify-between w-full">
                      <el-form-item class="!mb-3" label="中拱" label-width="50px">
                        <!-- <template #label>
                          <el-tooltip content="港内工况静水弯矩-中拱(kN·m)" placement="top">
                            <span class="inline-block width-[120px] text-nowrap overflow-hidden text-ellipsis whitespace-nowrap">港内工况静水弯矩-中拱</span>
                          </el-tooltip>
                        </template> -->
                        <el-input v-model="basicInfo.staSwbmHogg" placeholder="请输入港内工况静水弯矩-中拱(kN·m)" clearable class="w-full" />
                      </el-form-item>

                      <el-form-item class="!mb-0" label="中垂" label-width="50px">
                        <!-- <template #label>
                          <el-tooltip content="港内工况静水弯矩-中垂(kN·m)" placement="top">
                            <span class="inline-block width-[120px] text-nowrap overflow-hidden text-ellipsis whitespace-nowrap">港内工况静水弯矩-中垂</span>
                          </el-tooltip>
                        </template> -->
                        <el-input v-model="basicInfo.staSwbmSagg" placeholder="请输入港内工况静水弯矩-中垂(kN·m)" clearable class="w-full" />
                      </el-form-item>
                    </div>
                  </div>
                </div>
                <div class="grid grid-cols-1 col-span-3 relative auto-cols-fr gap-x-4 mb-[24px] ml-[37px]">
                  <div class="border-solid border-1 border-slate-200 flex flex-row justify-center rounded-md p-4">
                    <el-form-item label="CSA" class="!mb-0 w-full" prop="basicInfo.csa" label-width="50px">
                      <div class="flex w-full">
                        <el-select style="width: 70px" v-model="basicInfo.csaPrefix" class="mr-[8px]">
                          <el-option value="csa">
                            <span>★</span>
                            <span>CSA</span>
                          </el-option>
                          <el-option value="_csa">
                            <span class="underline underline-offset-2">★</span>
                            <span>CSA</span>
                          </el-option>
                          <template #label>
                            <div v-if="basicInfo.csaPrefix === '_csa'">
                              <span class="underline underline-offset-2">★</span>
                              <span>CSA</span>
                            </div>
                            <div v-if="basicInfo.csaPrefix === 'csa'">
                              <span>★</span>
                              <span>CSA</span>
                            </div>
                          </template>
                        </el-select>
                        <el-input v-model="basicInfo.csaInput" placeholder="请输入入级符号-CSA" clearable class="flex-1 py-0" type="textarea" :rows="1" />
                      </div>
                    </el-form-item>
                    <el-form-item label="CSM" class="!mb-0 w-full" prop="basicInfo.csm" label-width="50px">
                      <div class="flex w-full">
                        <el-select style="width: 70px" v-model="basicInfo.csmPrefix" class="mr-[8px]">
                          <el-option value="csm">
                            <span>★</span>
                            <span>CSM</span>
                          </el-option>
                          <el-option value="_csm">
                            <span class="underline underline-offset-2">★</span>
                            <span>CSM</span>
                          </el-option>
                          <el-option value="__csm">
                            <span class="underline underline-offset-2">★CSM</span>
                          </el-option>
                          <template #label>
                            <div v-if="basicInfo.csmPrefix === '_csm'">
                              <span class="underline underline-offset-2">★</span>
                              <span>CSA</span>
                            </div>
                            <div v-if="basicInfo.csmPrefix === 'csm'">
                              <span>★</span>
                              <span>CSA</span>
                            </div>
                            <div v-if="basicInfo.csmPrefix === '__csm'">
                              <span class="underline underline-offset-2">★CSA</span>
                            </div>
                          </template>
                        </el-select>
                        <el-input v-model="basicInfo.csmInput" placeholder="请输入入级符号-CSM" clearable class="flex-1 py-0" type="textarea" :rows="1" />
                      </div>
                    </el-form-item>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 第二行：结构特征 -->
          <div class="border-b border-gray-200 flex items-start mb-1 flex-col">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center w-[100px] flex-shrink-0 m-0 mb-2">
              <span class="w-1 h-6 bg-purple-500 mr-3"></span>
              结构特征
            </h3>
            <div class="flex-1 w-full">
              <div class="grid grid-cols-3 relative auto-cols-fr">
                <!-- 舷缘 -->
                <el-form-item label="舷缘" class="mb-0" prop="features.舷缘">
                  <el-select v-model="basicInfo.features['舷缘']" placeholder="请选择舷缘类型" clearable class="!w-full">
                    <el-option label="圆弧型" value="圆弧型" />
                    <el-option label="焊接型" value="焊接型" />
                  </el-select>
                </el-form-item>
                <!-- 船底 -->
                <el-form-item label="船底" class="mb-0" prop="features.船底">
                  <el-select v-model="basicInfo.features['船底']" placeholder="请选择船底类型" clearable class="!w-full">
                    <el-option label="双层底" value="双层底" />
                    <el-option label="单层底" value="单层底" />
                  </el-select>
                </el-form-item>
                <!-- 边舱 -->
                <el-form-item label="边舱" class="mb-0" prop="features.边舱">
                  <el-select v-model="basicInfo.features['边舱']" placeholder="请选择边舱类型" clearable class="!w-full">
                    <el-option label="顶边舱" value="顶边舱" />
                    <el-option label="底边舱" value="底边舱" />
                  </el-select>
                </el-form-item>
              </div>
              <div class="grid grid-cols-3 relative auto-cols-fr">
                <!-- 船底骨架 -->
                <el-form-item label="船底骨架" class="mb-0" prop="features.船底骨架">
                  <el-select v-model="basicInfo.features['船底骨架']" placeholder="请选择船底骨架类型" clearable class="!w-full">
                    <el-option label="横骨架式船底" value="横骨架式船底" />
                    <el-option label="纵骨架式船底" value="纵骨架式船底" />
                  </el-select>
                </el-form-item>
                <!-- 纵舱壁 -->
                <el-form-item label="纵舱壁" class="mb-0" prop="features.纵舱壁">
                  <el-select v-model="basicInfo.features['纵舱壁']" placeholder="请选择纵舱壁类型" clearable class="!w-full">
                    <el-option label="无纵舱壁" value="无纵舱壁" />
                    <el-option label="单纵舱壁" value="单纵舱壁" />
                    <el-option label="双纵舱壁" value="双纵舱壁" />
                  </el-select>
                </el-form-item>
                <!-- 是否有横撑（仅当选择双纵舱壁时显示） -->
                <el-form-item v-show="basicInfo.features['纵舱壁'] === '双纵舱壁'" label="是否有横撑" class="mb-0" prop="features.横撑">
                  <el-select v-model="basicInfo.features['横撑']" placeholder="请选择" clearable class="!w-full">
                    <el-option label="是" value="是" />
                    <el-option label="否" value="否" />
                  </el-select>
                </el-form-item>

                <!-- 舷侧骨架 -->
                <el-form-item label="舷侧骨架" class="mb-0" prop="features.舷侧骨架">
                  <el-select v-model="basicInfo.features['舷侧骨架']" placeholder="请选择舷侧骨架类型" clearable class="!w-full">
                    <el-option label="横骨架式舷侧" value="横骨架式舷侧" />
                    <el-option label="纵骨架式舷侧" value="纵骨架式舷侧" />
                  </el-select>
                </el-form-item>

                <!-- 甲板骨架 -->
                <el-form-item label="甲板骨架" class="mb-0" prop="features.甲板骨架">
                  <el-select v-model="basicInfo.features['甲板骨架']" placeholder="请选择甲板骨架类型" clearable class="!w-full">
                    <el-option label="横骨架式甲板" value="横骨架式甲板" />
                    <el-option label="纵骨架式甲板" value="纵骨架式甲板" />
                  </el-select>
                </el-form-item>

                <!-- 舱壁类型 -->
                <el-form-item label="舱壁" class="mb-0" prop="features.舱壁">
                  <el-select v-model="basicInfo.features['舱壁']" placeholder="请选择舱壁类型" clearable class="!w-full">
                    <el-option label="垂直槽型" value="垂直槽型" />
                    <el-option label="水平槽型" value="水平槽型" />
                  </el-select>
                </el-form-item>
                <!-- 底凳（仅当选择槽型舱壁时显示） -->
                <el-form-item v-show="basicInfo.features['舱壁'] === '垂直槽型' || basicInfo.features['舱壁'] === '水平槽型'" label="底凳" class="mb-0" prop="features.底凳">
                  <el-select v-model="basicInfo.features['底凳']" placeholder="请选择" clearable class="w-full">
                    <el-option label="有底凳" value="有底凳" />
                    <el-option label="无底凳" value="无底凳" />
                  </el-select>
                </el-form-item>

                <!-- 顶凳（仅当选择槽型舱壁时显示） -->
                <el-form-item v-show="basicInfo.features['舱壁'] === '垂直槽型' || basicInfo.features['舱壁'] === '水平槽型'" label="顶凳" class="mb-0" prop="features.顶凳">
                  <el-select v-model="basicInfo.features['顶凳']" placeholder="请选择" clearable class="w-full">
                    <el-option label="有顶凳" value="有顶凳" />
                    <el-option label="无顶凳" value="无顶凳" />
                  </el-select>
                </el-form-item>
                <!-- 舱壁结构 -->
                <el-form-item label="舱壁结构" class="mb-0" prop="features.舱壁结构">
                  <el-select v-model="basicInfo.features['舱壁结构']" placeholder="请选择舱壁结构" clearable class="w-full">
                    <el-option label="平面舱壁" value="平面舱壁" />
                    <el-option label="双层板舱壁" value="双层板舱壁" />
                    <el-option label="制荡舱壁" value="制荡舱壁" />
                  </el-select>
                </el-form-item>

                <!-- 球鼻艏 -->
                <el-form-item label="球鼻艏" class="mb-0" prop="features.球鼻艏">
                  <el-select v-model="basicInfo.features['球鼻艏']" placeholder="请选择" clearable class="w-full">
                    <el-option label="有球鼻艏" value="有球鼻艏" />
                    <el-option label="无球鼻艏" value="无球鼻艏" />
                  </el-select>
                </el-form-item>
                <!-- 船型 -->
                <el-form-item label="船型" class="mb-0" prop="features.船型">
                  <el-select v-model="basicInfo.features['船型']" placeholder="请选择船型" clearable class="w-full">
                    <el-option label="单体" value="单体" />
                    <el-option label="双体" value="双体" />
                    <el-option label="三体" value="三体" />
                    <el-option label="顶推链接" value="顶推链接" />
                    <el-option label="箱型船体" value="箱型船体" />
                  </el-select>
                </el-form-item>
                <!-- 上建 -->
                <el-form-item label="上建" class="mb-0 col-span-2" prop="features.上建">
                  <el-checkbox-group v-model="basicInfo.features['上建']" class="w-full flex flex-wrap gap-2">
                    <el-checkbox label="艏楼" value="艏楼" />
                    <el-checkbox label="艉楼" value="艉楼" />
                    <el-checkbox label="桥楼" value="桥楼" />
                    <el-checkbox label="长上建" value="长上建" />
                    <el-checkbox label="挡浪板" value="挡浪板" />
                  </el-checkbox-group>
                </el-form-item>
                <!-- 端部加强 -->
                <el-form-item label="端部加强" class="mb-0 col-span-2" prop="features.端部加强">
                  <el-checkbox-group v-model="basicInfo.features['端部加强']" class="w-full flex flex-wrap gap-2">
                    <el-checkbox label="船端加强" value="船端加强" />
                    <el-checkbox label="尾部船底加强" value="尾部船底加强" />
                    <el-checkbox label="艏部外漂" value="艏部外漂" />
                  </el-checkbox-group>
                </el-form-item>

                <!-- 设备 -->
                <el-form-item label="设备" class="mb-0 col-span-2" prop="features.设备">
                  <el-checkbox-group v-model="basicInfo.features['设备']" class="w-full flex flex-wrap gap-2">
                    <el-checkbox label="直升机甲板" value="直升机甲板" />
                    <el-checkbox label="车辆甲板" value="车辆甲板" />
                    <el-checkbox label="车辆跳板" value="车辆跳板" />
                  </el-checkbox-group>
                </el-form-item>
                <!-- 装载 -->
                <el-form-item label="装载" class="mb-0 col-span-2" prop="features.装载">
                  <el-checkbox-group v-model="basicInfo.features['装载']" class="w-full flex flex-wrap gap-2">
                    <el-checkbox label="重压载舱" value="重压载舱" />
                    <el-checkbox label="独立式液舱" value="独立式液舱" />
                    <el-checkbox label="整体式液舱" value="整体式液舱" />
                    <el-checkbox label="散货舱进水" value="散货舱进水" />
                  </el-checkbox-group>
                </el-form-item>
              </div>
            </div>
          </div>
          <!-- 第三行：功能附加 -->
          <div class="border-b border-gray-200 flex items-start mb-1 flex-col">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center w-[100px] flex-shrink-0 m-0 mb-2 md:mb-2 lg:mb-0 xl:mb-0">
              <span class="w-1 h-6 bg-green-500 mr-3"></span>
              功能附加
            </h3>
            <div class="flex-1">
              <div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 relative">
                <!-- 自动控制 -->
                <el-form-item label="自动控制" class="mb-0 md:col-span-1 lg:col-span-2 xl:col-span-4" prop="features.自动控制">
                  <el-checkbox-group v-model="basicInfo.features['自动控制']" class="w-full flex flex-wrap gap-2">
                    <el-checkbox label="机器处所周期无人值班" value="AUT-0" />
                    <el-checkbox label="机器处所集中控制" value="MCC" />
                    <el-checkbox label="驾驶室遥控" value="BRC" />
                  </el-checkbox-group>
                </el-form-item>
                <!-- 特殊检验 -->
                <el-form-item label="特殊检验" class="mb-0 col-span-4" prop="features.特殊检验">
                  <el-checkbox-group v-model="basicInfo.features['特殊检验']" class="w-full flex flex-wrap gap-2">
                    <el-checkbox label="加强检验程序" value="ESP" />
                    <el-checkbox label="水下检验" value="In-Water Survey" />
                    <el-checkbox label="船体循环检验" value="CHS" />
                    <el-checkbox label="轮机循环检验" value="CMS" />
                    <el-checkbox label="螺旋桨轴状态监控" value="SCM" />
                    <el-checkbox label="柴油机滑油状态监控" value="ECM" />
                    <el-checkbox label="机械计划保养系统" value="PMS" />
                    <el-checkbox label="衍射时差技术检测" value="TOFD" />
                    <el-checkbox label="相控阵超声检测对接焊缝" value="PAUT-Butt" />
                    <el-checkbox label="相控阵超声检测角焊缝" value="PAUT-Fillet" />
                    <el-checkbox label="船舶数字化检验" value="DDV" />
                  </el-checkbox-group>
                </el-form-item>
                <!-- 货物冷藏 -->
                <el-form-item label="货物冷藏" class="mb-0 col-span-4" prop="features.货物冷藏">
                  <el-checkbox-group v-model="basicInfo.features['货物冷藏']" class="w-full flex flex-wrap gap-2">
                    <el-checkbox label="水果保鲜" value="CF" />
                    <el-checkbox label="速冻" value="QF" />
                    <el-checkbox label="舱内载运冷藏集装箱" value="CRC" />
                    <el-checkbox label="舱内载运冷藏集装箱" value="AC f/WC" />
                  </el-checkbox-group>
                </el-form-item>

                <!-- 其他 -->
                <el-form-item label="其他" class="mb-0 col-span-4" prop="features.其他">
                  <el-checkbox-group v-model="basicInfo.features['其他']" class="w-full flex flex-wrap gap-2">
                    <el-checkbox label="船舶能效实时在线综合监控" value="EOM" />
                    <el-checkbox label="船体检查保养计划" value="HIMS" />
                    <el-checkbox label="海员起居舱室" value="Crew Accommodation(MLC)" />
                  </el-checkbox-group>
                </el-form-item>
              </div>
            </div>
          </div>
          <div class="flex justify-center space-x-4">
            <el-button size="large" @click="handleReset">重置表单</el-button>
            <el-button type="primary" size="large" :loading="submitting" @click="handleSubmit">
              {{ submitting ? '保存中...' : '保存' }}
            </el-button>
          </div>
        </div>
      </el-form>
      <el-divider></el-divider>
      <!-- 船舶图纸上传 -->
      <div class="mb-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <el-icon class="mr-2 text-green-500"><Document /></el-icon>
          船舶图纸
        </h2>
        <div class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <el-upload
            ref="structureUploadRef"
            v-model:file-list="drawingFiles"
            :auto-upload="true"
            :http-request="options => uploadFile(options.file, 'structure')"
            :on-progress="(event, file) => handleProgress(event, file, 'structure')"
            :on-success="(response, file) => handleSuccess(response, file, 'structure')"
            :on-error="(error, file) => handleError(error, file, 'structure')"
            accept=".pdf,.jpg,.png,.jpeg"
            :show-file-list="false"
            class="w-full h-[270px]"
            :before-upload="showSubtitleDialog"
            drag>
            <div class="text-center p-8">
              <el-icon class="text-6xl text-gray-400" size="30"><UploadFilled /></el-icon>
              <div class="text-gray-600">
                <h3 class="text-xl font-semibold mb-2 mt-0 text-center w-full">船舶结构</h3>
                <p class="text-gray-500 mb-3 text-center text-sm w-full">拖拽或点击上传</p>
                <el-progress v-if="uploadStatus.structure.isUploading" :percentage="uploadStatus.structure.progress" :stroke-width="5" :status="uploadStatus.structure.status" />
              </div>
            </div>
          </el-upload>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-x-3 relative auto-cols-fr">
          <div v-if="uploadedDrawing.length > 0" v-for="item in uploadedDrawing" :key="item.url" class="flex justify-center w-full">
            <PdfCanvas :cavansRender="false" :maxPage="1" class="w-full !h-[350px]" :url="item.url" />
          </div>
        </div>
      </div>
      <!-- 计算报告上传 -->
      <div class="mb-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <el-icon class="mr-2 text-purple-500"><DocumentCopy /></el-icon>
          计算模型
        </h2>
        <el-upload
          ref="modelUploadRef"
          v-model:file-list="modelFiles"
          :auto-upload="true"
          :show-file-list="false"
          :http-request="options => uploadFile(options.file, 'model')"
          :on-progress="(event, file) => handleProgress(event, file, 'model')"
          :on-success="(response, file) => handleSuccess(response, file, 'model')"
          :on-error="(error, file) => handleError(error, file, 'model')"
          multiple
          class="w-full h-[270px]"
          :before-upload="showSubtitleDialog"
          drag>
          <div class="text-center p-8">
            <el-icon class="text-6xl text-gray-400" size="30"><UploadFilled /></el-icon>
            <div class="text-gray-600">
              <h3 class="text-xl font-semibold mb-2 mt-0 text-center w-full">计算模型</h3>
              <p class="text-gray-500 mb-3 text-center text-sm w-full">拖拽或点击上传</p>
              <el-progress v-if="uploadStatus.model.isUploading" :percentage="uploadStatus.model.progress" :stroke-width="5" :status="uploadStatus.model.status" />
            </div>
          </div>
        </el-upload>
        <div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-x-3 relative auto-cols-fr">
          <div v-if="uploadModels.length > 0" v-for="item in uploadModels" :key="item.url" class="flex justify-center w-full">
            <PdfCanvas :cavansRender="false" :maxPage="1" class="w-full !h-[350px]" :url="item.url" />
          </div>
        </div>
      </div>
      <el-divider></el-divider>

      <div class="mb-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <el-icon class="mr-2 text-purple-500"><DocumentCopy /></el-icon>
          评估报告
        </h2>
        <el-upload
          ref="reportUploadRef"
          v-model:file-list="reportFiles"
          :auto-upload="true"
          :show-file-list="false"
          :http-request="options => uploadFile(options.file, 'report')"
          :on-progress="(event, file) => handleProgress(event, file, 'report')"
          :on-success="(response, file) => handleSuccess(response, file, 'report')"
          :on-error="(error, file) => handleError(error, file, 'report')"
          multiple
          class="w-full h-[270px]"
          :before-upload="showSubtitleDialog"
          drag>
          <div class="text-center p-8">
            <el-icon class="text-6xl text-gray-400" size="30"><UploadFilled /></el-icon>
            <div class="text-gray-600">
              <h3 class="text-xl font-semibold mb-2 mt-0 text-center w-full">评估报告</h3>
              <p class="text-gray-500 mb-3 text-center text-sm w-full">拖拽或点击上传</p>
              <el-progress v-if="uploadStatus.report.isUploading" :percentage="uploadStatus.report.progress" :stroke-width="5" :status="uploadStatus.report.status" />
            </div>
          </div>
        </el-upload>
        <div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-x-3 relative auto-cols-fr">
          <div v-if="uploadReports.length > 0" v-for="item in uploadReports" :key="item.url" class="flex justify-center w-full">
            <PdfCanvas :cavansRender="false" :maxPage="1" class="w-full !h-[350px]" :url="item.url" />
          </div>
        </div>
      </div>
      <el-divider></el-divider>
      <el-form :model="loadInfo" label-width="120px" class="space-y-6" ref="loadInfoFormRef">
        <!-- 装载信息 -->
        <div class="mb-6">
          <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
            <el-icon class="mr-2 text-orange-500"><Box /></el-icon>
            装载信息
          </h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <el-form-item label="载重吨(t)" prop="载重吨">
              <el-input-number v-model="loadInfo['载重吨']" :min="0" :precision="1" placeholder="载重吨" class="w-full" />
            </el-form-item>

            <el-form-item label="总吨位(GT)" prop="总吨位">
              <el-input-number v-model="loadInfo['总吨位']" :min="0" :precision="1" placeholder="总吨位" class="w-full" />
            </el-form-item>

            <el-form-item label="净吨位(NT)" prop="净吨位">
              <el-input-number v-model="loadInfo['净吨位']" :min="0" :precision="1" placeholder="净吨位" class="w-full" />
            </el-form-item>

            <el-form-item label="货舱容积(m³)" prop="货舱容积">
              <el-input-number v-model="loadInfo['货舱容积']" :min="0" :precision="1" placeholder="货舱容积" class="w-full" />
            </el-form-item>

            <el-form-item label="最大装载量(t)" prop="最大装载量">
              <el-input-number v-model="loadInfo['最大装载量']" :min="0" :precision="1" placeholder="最大装载量" class="w-full" />
            </el-form-item>

            <el-form-item label="燃油容量(t)" prop="燃油容量">
              <el-input-number v-model="loadInfo['燃油容量']" :min="0" :precision="1" placeholder="燃油容量" class="w-full" />
            </el-form-item>
          </div>

          <el-form-item label="装载备注" prop="装载备注">
            <el-input v-model="loadInfo['装载备注']" type="textarea" :rows="2" placeholder="请输入装载相关的备注信息" maxlength="300" show-word-limit />
          </el-form-item>
          <div class="flex justify-center space-x-4">
            <el-button size="large" @click="handleReset">重置表单</el-button>
            <el-button type="primary" size="large" :loading="submitting" @click="submitLoadInfo">
              {{ submitting ? '保存中...' : '保存' }}
            </el-button>
            <el-upload :before-upload="showSubtitleDialog" :http-request="options => uploadFile(options.file, 'loading')" :show-file-list="false">
              <el-button type="primary" size="large" :loading="uploadStatus.loading.isUploading">导入装载信息</el-button>
            </el-upload>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, DocumentCopy, Box, Folder, UploadFilled, ArrowUp, ArrowDown, Search, Ship, CirclePlus } from '@element-plus/icons-vue'
import Apis from '@/apis/index'
import { basicRulesObj, basicInfoObj, loadInfoObj, drawingInfoObj } from './config'
import PdfCanvas from '@/components/PdfCanvas/index.vue'
import _ from 'lodash'
// const path = require('path')
const path = window.electronAPI.path
// 表单引用
const formRef = ref(null)

// 文件上传引用
const drawingUploadRef = ref(null)
const reportUploadRef = ref(null)
const otherUploadRef = ref(null)

const basicInfo = reactive(_.cloneDeep(basicInfoObj))
const drawingInfo = reactive(_.cloneDeep(drawingInfoObj))
// 表单数据
const loadInfo = reactive(_.cloneDeep(loadInfoObj))

// 文件列表
const drawingFiles = ref([])
const modelFiles = ref([])
const reportFiles = ref([])

// 上传状态管理
const uploadStatus = reactive({
  structure: { isUploading: false, progress: 0, status: '' },
  model: { isUploading: false, progress: 0, status: '' },
  report: { isUploading: false, progress: 0, status: '' },
  loading: { isUploading: false, progress: 0, status: '' }
})
// 测试：使用public目录的PDF文件（推荐方案）
const uploadedDrawing = ref([])
const uploadModels = ref([])
const uploadReports = ref([])
// 提交状态
const submitting = ref(false)
// 表单验证规则
const basicRules = _.cloneDeep(basicRulesObj)
const currentShipId = ref(null)
const currentSubtitle = ref('')
// 处理表单提交
const handleSubmit = async () => {
  if (!formRef) return
  await formRef.value.validate(async (valid, fields) => {
    if (valid) {
      try {
        let keelDate = basicInfo.keelDate
        if (keelDate) {
          keelDate = keelDate + ' 00:00:00'
        }
        const features = _.cloneDeep(basicInfo.features)
        for (const key in features) {
          if (Array.isArray(features[key])) {
            if (features[key].length === 0) {
              delete features[key]
            } else {
              features[key] = features[key].join(',')
            }
          }
          if (!features[key]) {
            delete features[key]
          }
        }
        const csa = basicInfo.csaPrefix + basicInfo.csaInput
        const csm = basicInfo.csmPrefix + basicInfo.csmInput
        // 调用API保存船舶信息
        const response = await Apis.general.post_shipstruct_ship_struct_addship({
          data: { ...basicInfo, features, keelDate, csa, csm }
        })
        if (response.code === 200) {
          ElMessage.success('保存成功')
          currentShipId.value = response.data
        } else {
          ElMessage.error(response.msg || '保存失败')
        }
      } catch (error) {
        console.log(error, 'error')
      } finally {
        submitting.value = false
      }
    }
  })
}
const loadInfoFormRef = ref(null)
// 重置表单
const handleReset = () => {
  loadInfoFormRef.value.resetFields()
}
const handleAddDeck = () => {}

// 文件上传相关函数
const uploadFile = async (file, type) => {
  const keyMap = { structure: '船舶结构', model: '计算模型', report: '评估报告', loading: '装载信息' }
  let list
  switch (type) {
    case 'structure':
      list = uploadedDrawing
      break
    case 'model':
      list = uploadModels
      break
    case 'report':
      list = uploadReports
      break
  }
  uploadStatus[type].isUploading = true
  uploadStatus[type].progress = 0
  uploadStatus[type].status = ''
  const formData = new FormData()
  formData.append('file', file)
  formData.append('type', keyMap[type])
  formData.append('shipId', currentShipId.value || 9)
  formData.append('subtitle', currentSubtitle.value)
  try {
    // 使用axios直接上传FormData，确保正确设置content-type
    const res = await Apis.general.post_shipstruct_ship_uploadreport({
      data: formData,
      // headers: {
      //   'Content-Type': 'multipart/form-data'
      // },
      onUploadProgress: progressEvent => {
        // 上传过程中最多到80%
        const percent = Math.min(Math.floor((progressEvent.loaded / progressEvent.total) * 80), 80)
        uploadStatus[type].progress = percent
      }
    })
    if (res.code === 200) {
      // 上传完成后设为100%
      uploadStatus[type].progress = 100
      uploadStatus[type].status = 'success'
      ElMessage.success('上传成功')
      const url = res.data
      const filename = path.basename(url)
      list && list.value.push({ url, title: filename })
    } else {
      // uploadStatus[type].isUploading = false
      uploadStatus[type].progress = 100
      uploadStatus[type].status = 'exception'
      ElMessage.error(res.msg || '上传失败')
    }
  } catch (error) {
    if (type === 'loading') {
      uploadStatus[type].isUploading = false
      return
    }
    uploadStatus[type].progress = 100
    // uploadStatus[type].isUploading = false
    uploadStatus[type].status = 'exception'
    ElMessage.error('上传失败：' + (error.message || '未知错误'))
  }
}
const showSubtitleDialog = async file => {
  try {
    const res = await ElMessageBox.prompt('', '文件描述', {
      confirmButtonText: '确认',
      cancelButtonText: '跳过'
    })
    currentSubtitle.value = res.value
    return true
  } catch (error) {
    currentSubtitle.value = ''
    return true
  }
}

// 处理上传进度
const handleProgress = (event, file, type) => {}

// 处理上传成功
const handleSuccess = (response, file, type) => {}

// 处理上传错误
const handleError = (error, file, type) => {}
const submitLoadInfo = async () => {
  const submitInfo = _.cloneDeep(loadInfo)
  for (const key in submitInfo) {
    if (Array.isArray(submitInfo[key]) && submitInfo[key].length === 0) {
      delete submitInfo[key]
    }
    if (submitInfo[key] === null || submitInfo[key] === '') {
      delete submitInfo[key]
    }
  }
  const res = await Apis.general.post_shipstruct_ship_struct_addlc({
    data: { features: submitInfo, id: currentShipId.value || 9 }
  })
  if (res.code === 200) {
    ElMessage.success('保存成功')
  } else {
    ElMessage.error(res.msg || '保存失败')
  }
}
</script>

<style lang="scss" scoped>
@import './style.scss';
</style>
