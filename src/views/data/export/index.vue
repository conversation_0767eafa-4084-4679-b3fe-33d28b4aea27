<template>
  <div class="p-5">
    <div class="max-w-6xl mx-auto">
      <!-- 搜索区域 -->
      <el-form :model="searchForm" inline>
        <el-form-item label="起始时间" class="w-[200px]">
          <el-date-picker v-model="searchForm.exportTimeStart" type="date" placeholder="请选择开始时间" value-format="YYYY-MM-DD" style="width: 200px" />
        </el-form-item>

        <el-form-item label="截止时间" class="w-[200px]">
          <el-date-picker v-model="searchForm.exportTimeEnd" type="date" placeholder="请选择结束时间" value-format="YYYY-MM-DD" style="width: 200px" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 导出记录表格 -->
      <el-table :data="exportRecords" style="width: 100%" stripe border v-loading="loading">
        <el-table-column prop="id" label="记录ID" width="100" fixed="left" />
        <el-table-column prop="exportTime" label="导出时间" width="180">
          <template #default="scope">
            <span>{{ formatTime(scope.row.exportTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="verifyCode" label="验证码" width="140">
          <template #default="scope">
            <div class="flex items-center space-x-2">
              <span>{{ scope.row.verifyCode }}</span>
              <el-button v-if="scope.row.verifyCode" link size="small" @click="copyCode(scope.row.verifyCode)">📋</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="filePath" label="文件路径" min-width="300" show-overflow-tooltip />
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleDownload(scope.row)" :loading="scope.row.downloading" link>下载</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-end items-end mt-6 flex-col-reverse">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="20"
          :small="false"
          :disabled="loading"
          :background="true"
          layout="prev, pager, next, jumper,total"
          :total="total"
          :pager-count="7"
          @current-change="handleCurrentChange" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import Apis from '@/apis'

// 响应式数据
const searchForm = reactive({
  exportTimeStart: '',
  exportTimeEnd: ''
})

const loading = ref(false)
const currentPage = ref(1)

const total = ref(0)
const exportRecords = ref([])

// 方法
const fetchExportRecords = async () => {
  loading.value = true
  try {
    // 格式化时间，添加时分秒
    const formatStartTime = searchForm.exportTimeStart ? `${searchForm.exportTimeStart} 00:00:00` : ''
    const formatEndTime = searchForm.exportTimeEnd ? `${searchForm.exportTimeEnd} 23:59:59` : ''

    const response = await Apis.general.get_shipstruct_admin_ship_exportrecord_getrecords({
      params: {
        exportTimeStart: formatStartTime,
        exportTimeEnd: formatEndTime
      },
      data: {
        exportTimeStart: formatStartTime,
        exportTimeEnd: formatEndTime
      },
      // 禁用缓存，确保每次都是最新数据
      cacheFor: 0
    })

    if (response.code === 200) {
      // 为每条记录添加下载状态
      exportRecords.value = response.data.rows.map(item => ({
        ...item,
        downloading: false
      }))
      total.value = parseInt(response.data.total)
    } else {
      ElMessage.error(response.msg || '获取导出记录失败')
    }
  } catch (error) {
    console.error('获取导出记录失败:', error)
    ElMessage.error('获取导出记录失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchExportRecords()
}

const handleReset = () => {
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(endDate.getDate() - 7)
  searchForm.exportTimeStart = startDate.toISOString().split('T')[0]
  searchForm.exportTimeEnd = endDate.toISOString().split('T')[0]
  currentPage.value = 1
  fetchExportRecords()
  ElMessage.info('已重置搜索条件')
}

const copyCode = code => {
  navigator.clipboard
    .writeText(code)
    .then(() => {
      ElMessage.success('验证码已复制到剪贴板')
    })
    .catch(() => {
      ElMessage.error('复制失败')
    })
}

const handleCurrentChange = val => {
  currentPage.value = val
  fetchExportRecords()
}

const handleDownload = async row => {
  if (!row.id) {
    ElMessage.error('记录ID无效')
    return
  }

  // 设置下载状态
  row.downloading = true

  try {
    const response = await Apis.general.post_shipstruct_admin_ship_exportrecord_download({
      params: {
        id: parseInt(row.id)
      },
      responseType: 'blob'
    })
    console.log(response, 'response22')
    // 处理文件下载
    if (response.data instanceof Blob) {
      const url = window.URL.createObjectURL(response.data)
      const link = document.createElement('a')
      link.href = url
      link.download = row.filePath ? row.filePath.split('/').pop() : `export_${row.id}.zip`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      ElMessage.success('文件下载成功')
    } else {
      ElMessage.error('下载失败，返回格式异常')
    }
  } catch (error) {
    console.error('下载失败:', error)
  } finally {
    row.downloading = false
  }
}

const formatTime = timeStr => {
  if (!timeStr) return '未设置'

  // 如果是时间戳，转换为日期
  if (/^\d+$/.test(timeStr)) {
    const date = new Date(parseInt(timeStr))
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  // 如果已经是格式化的时间字符串，直接返回
  return timeStr
}

// 生命周期
onMounted(() => {
  // 设置默认时间范围为最近7天
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(endDate.getDate() - 7)

  searchForm.exportTimeStart = startDate.toISOString().split('T')[0]
  searchForm.exportTimeEnd = endDate.toISOString().split('T')[0]

  fetchExportRecords()
})
</script>

<style scoped>
:deep(.el-table) {
  border-radius: 0.5rem;
}

:deep(.el-table th) {
  background-color: #f8fafc;
}
</style>
