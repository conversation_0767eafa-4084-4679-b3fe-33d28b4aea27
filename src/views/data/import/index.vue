<template>
  <div class="p-5">
    <div class="max-w-7xl mx-auto">
      <!-- 导入操作区域 -->
      <div class="p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-end">
          <!-- 文件选择 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">选择ZIP文件</label>
            <el-upload ref="uploadRef" :auto-upload="false" :show-file-list="false" accept=".zip" :on-change="handleFileChange" class="w-full">
              <el-button :icon="Upload" type="primary" :disabled="loading" class="w-full">
                {{ selectedFile ? selectedFile.name : '选择ZIP文件' }}
              </el-button>
            </el-upload>
          </div>

          <!-- 解压密码 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">解压密码</label>
            <el-input
              v-model="password"
              type="password"
              autocomplete="new-password"
              :autocomplete="false"
              placeholder="请输入解压密码"
              :disabled="loading"
              show-password
              @keyup.enter="handleExtract" />
          </div>

          <!-- 解压按钮 -->
          <div>
            <el-button type="success" :icon="FolderOpened" :loading="loading" :disabled="!selectedFile" @click="handleExtract" class="w-full">
              {{ loading ? '解压中...' : '解压导入' }}
            </el-button>
          </div>
        </div>

        <!-- 进度信息 -->
        <div v-if="loading" class="mt-4">
          <el-progress :percentage="progress" :stroke-width="8" />
          <p class="text-sm text-gray-600 mt-2 text-center">
            {{ progressText }}
          </p>
        </div>
      </div>

      <!-- 导入结果 -->
      <div class="p-6" v-if="importedData.length > 0">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-semibold text-gray-800">导入结果 ({{ importedData.length }} 条记录)</h2>
          <el-button type="primary" :icon="Download" @click="handleExportResult">导出结果</el-button>
        </div>
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-2xl font-bold text-gray-800">船舶列表</h2>
          <!-- 列显示控制 -->
          <el-dropdown trigger="click" placement="bottom-end">
            <el-button size="small" type="primary" plain>
              <el-icon class="mr-1"><Setting /></el-icon>
              列显示设置
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <div class="p-3 min-w-48">
                  <div class="text-sm font-medium text-gray-700 mb-2">选择显示列</div>
                  <div class="grid grid-cols-3 gap-2 max-h-80 overflow-y-auto">
                    <el-checkbox v-model="columnVisible.shipLoa" size="small">总长LOA</el-checkbox>
                    <el-checkbox v-model="columnVisible.shipLpp" size="small">垂线间长</el-checkbox>
                    <el-checkbox v-model="columnVisible.shipLrule" size="small">规范船长</el-checkbox>
                    <el-checkbox v-model="columnVisible.beamArch" size="small">梁拱</el-checkbox>
                    <el-checkbox v-model="columnVisible.moldDepth" size="small">型深</el-checkbox>
                    <el-checkbox v-model="columnVisible.moldWidth" size="small">型宽</el-checkbox>
                    <el-checkbox v-model="columnVisible.structDraft" size="small">结构吃水</el-checkbox>
                    <el-checkbox v-model="columnVisible.designDraft" size="small">设计吃水</el-checkbox>
                    <el-checkbox v-model="columnVisible.dySwbmHogg" size="small">航行中拱</el-checkbox>
                    <el-checkbox v-model="columnVisible.dySwbmSagg" size="small">航行中垂</el-checkbox>
                    <el-checkbox v-model="columnVisible.staSwbmHogg" size="small">港内中拱</el-checkbox>
                    <el-checkbox v-model="columnVisible.staSwbmSagg" size="small">港内中垂</el-checkbox>
                    <el-checkbox v-model="columnVisible.dwt" size="small">排水量</el-checkbox>
                    <el-checkbox v-model="columnVisible.lsw" size="small">空船重量</el-checkbox>
                    <el-checkbox v-model="columnVisible.ndw" size="small">净载重量</el-checkbox>
                    <el-checkbox v-model="columnVisible.csa" size="small">结构设备符号</el-checkbox>
                    <el-checkbox v-model="columnVisible.csm" size="small">机械电气符号</el-checkbox>
                    <el-checkbox v-model="columnVisible.keelDate" size="small">铺龙骨日期</el-checkbox>
                  </div>
                  <div class="mt-3 pt-2 border-t border-gray-200">
                    <el-button size="small" type="primary" @click="resetColumns">显示全部</el-button>
                  </div>
                </div>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <!-- 数据表格 -->
        <el-table :data="importedData" style="width: 100%" stripe border max-height="600">
          <!-- 基本信息列 -->
          <el-table-column prop="shipName" label="船名" width="160" fixed="left" />
          <el-table-column prop="shipOwner" label="船东" width="160" />
          <el-table-column prop="shipType" label="船舶类型" width="140" />
          <el-table-column prop="area" label="航区" width="120" />
          <el-table-column prop="shipYard" label="船厂" width="150" />
          <el-table-column prop="imo" label="IMO号" width="120" />

          <!-- 可选显示的列 -->
          <el-table-column v-if="columnVisible.shipLoa" prop="shipLoa" label="总长LOA(m)" width="120" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.shipLoa ? Number(scope.row.shipLoa).toFixed(2) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.shipLpp" prop="shipLpp" label="垂线间长LPP(m)" width="140" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.shipLpp ? Number(scope.row.shipLpp).toFixed(2) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.shipLrule" prop="shipLrule" label="规范船长(m)" width="120" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.shipLrule ? Number(scope.row.shipLrule).toFixed(2) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.beamArch" prop="beamArch" label="梁拱(m)" width="100" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.beamArch ? Number(scope.row.beamArch).toFixed(2) : '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column v-if="columnVisible.moldDepth" prop="moldDepth" label="型深(m)" width="100" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.moldDepth ? Number(scope.row.moldDepth).toFixed(2) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.moldWidth" prop="moldWidth" label="型宽(m)" width="100" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.moldWidth ? Number(scope.row.moldWidth).toFixed(2) : '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column v-if="columnVisible.structDraft" prop="structDraft" label="结构吃水(m)" width="120" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.structDraft ? Number(scope.row.structDraft).toFixed(2) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.designDraft" prop="designDraft" label="设计吃水(m)" width="120" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.designDraft ? Number(scope.row.designDraft).toFixed(2) : '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column v-if="columnVisible.dySwbmHogg" prop="dySwbmHogg" label="航行中拱(kNm)" width="120" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.dySwbmHogg ? Number(scope.row.dySwbmHogg).toFixed(0) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.dySwbmSagg" prop="dySwbmSagg" label="航行中垂(kNm)" width="120" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.dySwbmSagg ? Number(scope.row.dySwbmSagg).toFixed(0) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.staSwbmHogg" prop="staSwbmHogg" label="港内中拱(kNm)" width="120" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.staSwbmHogg ? Number(scope.row.staSwbmHogg).toFixed(0) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.staSwbmSagg" prop="staSwbmSagg" label="港内中垂(kNm)" width="120" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.staSwbmSagg ? Number(scope.row.staSwbmSagg).toFixed(0) : '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column v-if="columnVisible.dwt" prop="dwt" label="排水量(t)" width="100" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.dwt ? Number(scope.row.dwt).toFixed(0) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.lsw" prop="lsw" label="空船重量(t)" width="110" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.lsw ? Number(scope.row.lsw).toFixed(0) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.ndw" prop="ndw" label="净载重量(t)" width="110" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.ndw ? Number(scope.row.ndw).toFixed(0) : '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column v-if="columnVisible.csa" prop="csa" label="结构设备符号" width="120">
            <template #default="scope">
              {{ scope.row.csa || '-' }}
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.csm" prop="csm" label="机械电气符号" width="120">
            <template #default="scope">
              {{ scope.row.csm || '-' }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && importedData.length === 0" class="p-12 text-center">
        <el-icon size="64" class="text-gray-400 mb-4">
          <FolderOpened />
        </el-icon>
        <h3 class="text-lg font-medium text-gray-600 mb-2">暂无导入数据</h3>
        <p class="text-gray-400">请选择ZIP文件并解压导入船舶数据</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, FolderOpened, Download } from '@element-plus/icons-vue'

// 响应式数据
const selectedFile = ref(null)
const password = ref('')
const loading = ref(false)
const progress = ref(0)
const progressText = ref('')
const importedData = ref([])
const columnVisible = ref({
  shipLoa: true,
  shipLpp: false,
  shipLrule: false,
  beamArch: false,
  moldDepth: true,
  moldWidth: true,
  structDraft: false,
  designDraft: false,
  dySwbmHogg: false,
  dySwbmSagg: false,
  staSwbmHogg: false,
  staSwbmSagg: false,
  dwt: true,
  lsw: false,
  ndw: false,
  csa: false,
  csm: false,
  keelDate: false
})
// 重置列显示为默认
const resetColumns = () => {
  columnVisible.value = {
    shipLoa: true,
    shipLpp: true,
    shipLrule: true,
    beamArch: true,
    moldDepth: true,
    moldWidth: true,
    structDraft: true,
    designDraft: true,
    dySwbmHogg: true,
    dySwbmSagg: true,
    staSwbmHogg: true,
    staSwbmSagg: true,
    dwt: true,
    lsw: true,
    ndw: true,
    csa: true,
    csm: true,
    keelDate: true
  }
}
// 文件选择处理
const handleFileChange = file => {
  selectedFile.value = file.raw
  importedData.value = []
}
let interval = null
// 解压导入处理
const handleExtract = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择ZIP文件')
    return
  }
  if (!password.value) {
    ElMessage.warning('请输入解压密码')
    return
  }
  loading.value = true
  progress.value = 0
  progressText.value = '正在读取文件...'

  try {
    // 模拟解析数据
    progressText.value = '正在解析船舶数据...'
    interval = setInterval(() => {
      if (progress.value <= 80) {
        progress.value += 10
      }
    }, 1000)
    const formData = new FormData()
    formData.append('file', selectedFile.value)
    // formData.append('password', password.value)
    const res = await Apis.general.post_shipstruct_ship_import({
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      params: {
        verifyCode: password.value
      },
      data: formData
    })
    if (res.code === 200) {
      importedData.value = res.data
      ElMessage.success = '解压成功'
    } else {
      ElMessage.error(res.message)
    }
  } catch (error) {
    console.log(error, 'error')
  } finally {
    interval && clearInterval(interval)
    loading.value = false
    progress.value = 0
    progressText.value = ''
  }
}

// 导出结果
const handleExportResult = () => {
  if (importedData.value.length === 0) {
    ElMessage.warning('暂无数据可导出')
    return
  }

  ElMessageBox.confirm(`确定要导出 ${importedData.value.length} 条船舶数据吗？`, '导出确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  })
    .then(() => {
      // 这里实现实际的导出逻辑
      ElMessage.success('导出成功')
    })
    .catch(() => {
      // 取消导出
    })
}
</script>

<style scoped>
.el-upload {
  width: 100%;
}

:deep(.el-upload .el-button) {
  width: 100%;
}
</style>
