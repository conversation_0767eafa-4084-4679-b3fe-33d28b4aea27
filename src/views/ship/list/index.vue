<template>
  <div class="p-5">
    <div class="max-w-7xl mx-auto">
      <!-- 搜索过滤区域 -->
      <div>
        <el-form :model="searchForm" label-width="100px">
          <!-- 第一行：基本信息 -->
          <div class="border-b border-gray-200 flex items-start mb-1 flex-col">
            <div class="text-lg font-semibold text-gray-800 flex items-center w-[100px] flex-shrink-0 mb-2">
              <span class="w-1 h-6 bg-blue-500 mr-3 rounded-sm"></span>
              基本信息
            </div>

            <!-- 折叠按钮 -->
            <div class="flex-1 w-full">
              <div class="grid grid-cols-3 relative pr-[65px]">
                <el-button @click="toggleExpanded" link class="absolute top-0 right-2 text-gray-500 hover:text-blue-500 transition-colors" :icon="isExpanded ? ArrowUp : ArrowDown" size="small">
                  {{ isExpanded ? '收起' : '展开' }}
                </el-button>
                <el-form-item label="船名" class="mb-0">
                  <el-input v-model="searchForm.shipName" placeholder="请输入船名" clearable class="w-full">
                    <template #prefix>
                      <el-icon><Search /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item label="船东" class="mb-0">
                  <el-input v-model="searchForm.shipOwner" placeholder="请输入船东名称" clearable class="w-full" />
                </el-form-item>

                <el-form-item label="船厂" class="mb-0">
                  <el-input v-model="searchForm.shipYard" placeholder="请输入船厂" clearable class="w-full"></el-input>
                </el-form-item>
              </div>
              <el-collapse-transition>
                <div v-show="isExpanded" class="grid grid-cols-3 relative pr-[65px] auto-cols-fr">
                  <!-- 船舶类型 -->
                  <el-form-item label="船舶类型" class="mb-0">
                    <el-select v-model="searchForm.shipType" placeholder="请选择船舶类型" clearable class="w-full">
                      <el-option label="普通干货船" value="普通干货船" />
                      <el-option label="甲板货船" value="甲板货船" />
                      <el-option label="冷藏货船" value="冷藏货船" />
                      <el-option label="滚装船" value="滚装船" />
                      <el-option label="散货船" value="散货船" />
                      <el-option label="油船" value="油船" />
                      <el-option label="矿/油船" value="矿/油船" />
                      <el-option label="矿/散/油船" value="矿/散/油船" />
                      <el-option label="集装箱船" value="集装箱船" />
                      <el-option label="敞口集装箱船" value="敞口集装箱船" />
                      <el-option label="油驳" value="油驳" />
                      <el-option label="化学品驳" value="化学品驳" />
                      <el-option label="气体运输驳" value="气体运输驳" />
                      <el-option label="箱形驳" value="箱形驳" />
                      <el-option label="化学品液货船" value="化学品液货船" />
                      <el-option label="液化气体船" value="液化气体船" />
                      <el-option label="LNG运输船" value="LNG运输船" />
                      <el-option label="CNG运输船" value="CNG运输船" />
                      <el-option label="化学品/油液货船" value="化学品/油液货船" />
                      <el-option label="极地破冰船" value="极地破冰船" />
                      <el-option label="加强型破冰船" value="加强型破冰船" />
                      <el-option label="固定锚地储油船" value="固定锚地储油船" />
                      <el-option label="浮油回收船" value="浮油回收船" />
                    </el-select>
                  </el-form-item>
                  <!-- 航区 -->
                  <el-form-item label="航区" class="mb-0">
                    <el-select v-model="searchForm.area" placeholder="请选择航区" clearable class="w-full">
                      <el-option label="1类航区" value="R1" />
                      <el-option label="2类航区" value="R2" />
                      <el-option label="3类航区" value="R3" />
                      <el-option label="非国际1类航区" value="R1(D)" />
                      <el-option label="非国际2类航区" value="R2(D)" />
                      <el-option label="非国际3类航区" value="R3(D)" />
                      <el-option label="特定航线" value="xx-xx Service" />
                      <el-option label="特定航区" value="SZ(XX)" />
                      <el-option label="特定航线船体结构强度评估" value="SL(XX-YY)" />
                      <el-option label="远海营运限制" value="Open Sea Service Restriction" />
                      <el-option label="近海营运限制" value="Greater Coastal Service Restriction" />
                      <el-option label="沿海营运限制" value="Coastal Service Restriction" />
                      <el-option label="遮蔽营运限制" value="Sheltered Water Service Restriction" />
                      <el-option label="平静水域营运限制" value="Calm Water Service Restriction" />
                      <el-option label="营运气象" value="Weather Restriction N" />
                      <el-option label="在3类航区内作业" value="Dredging Within R3" />
                      <el-option label="在2类航区内作业" value="Dredging Within R2" />
                      <el-option label="在1类航区内作业" value="Dredging Within R1" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="IMO编号" class="mb-0">
                    <el-input v-model="searchForm.imo" placeholder="请输入IMO编号" clearable class="w-full"></el-input>
                  </el-form-item>
                  <el-form-item label="排水量" class="mb-0">
                    <range-input v-model:min="searchForm.minDwt" v-model:max="searchForm.maxDwt" prePlaceholder="最小值(t)" nextPlaceholder="最大值(t)" />
                  </el-form-item>

                  <el-form-item label="总长" class="mb-0">
                    <range-input v-model:min="searchForm.minShipLoa" v-model:max="searchForm.maxShipLoa" prePlaceholder="最小值(m)" nextPlaceholder="最大值(m)" />
                  </el-form-item>

                  <el-form-item label="型宽" class="mb-0">
                    <range-input v-model:min="searchForm.minMoldWidth" v-model:max="searchForm.maxMoldWidth" prePlaceholder="最小值(m)" nextPlaceholder="最大值(m)" />
                  </el-form-item>

                  <el-form-item label="型深" class="mb-0">
                    <range-input v-model:min="searchForm.minMoldDepth" v-model:max="searchForm.maxMoldDepth" prePlaceholder="最小值(m)" nextPlaceholder="最大值(m)" />
                  </el-form-item>

                  <el-form-item label="结构吃水" class="mb-0">
                    <range-input v-model:min="searchForm.minStructDraft" v-model:max="searchForm.maxStructDraft" prePlaceholder="最小值(m)" nextPlaceholder="最大值(m)" />
                  </el-form-item>

                  <el-form-item label="设计吃水" class="mb-0">
                    <range-input v-model:min="searchForm.minDesignDraft" v-model:max="searchForm.maxDesignDraft" prePlaceholder="最小值(m)" nextPlaceholder="最大值(m)" />
                  </el-form-item>
                  <div class="grid grid-cols-12 col-span-3">
                    <el-form-item class="mb-0 col-span-5">
                      <template #label>
                        <span>
                          <span class="underline-offset-1 underline">★</span>
                          CSA
                        </span>
                      </template>
                      <el-radio-group v-model="searchForm.csa" class="w-full flex flex-wrap gap-2">
                        <el-radio value="">不限</el-radio>
                        <el-radio value="csa">★CSA</el-radio>
                        <el-radio value="_csa">
                          <span>
                            <span class="underline-offset-1 underline">★</span>
                            CSA
                          </span>
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item class="mb-0 col-span-7">
                      <template #label>
                        <span>
                          <span class="underline-offset-1 underline">★</span>
                          CSM
                        </span>
                      </template>
                      <el-radio-group v-model="searchForm.csm" class="w-full flex flex-wrap gap-2">
                        <el-radio value="">不限</el-radio>
                        <el-radio value="csm">★CSM</el-radio>
                        <el-radio value="_csm">
                          <span>
                            <span class="underline-offset-1 underline">★</span>
                            CSM
                          </span>
                        </el-radio>
                        <el-radio value="__csm">
                          <span class="underline-offset-1 underline">★CSM</span>
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </div>
                </div>
              </el-collapse-transition>
            </div>
          </div>

          <!-- 第二行：结构特征 -->
          <div class="flex items-start mb-1 flex-col">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center m-0 w-[100px] flex-shrink-0 mb-2">
              <span class="w-1 h-6 bg-purple-500 mr-3 rounded-sm"></span>
              结构特征
            </h3>
            <div class="flex-1 w-full">
              <div class="grid grid-cols-3 relative pr-[65px]">
                <el-button @click="toggleExpanded2" link class="absolute top-0 right-2 text-gray-500 hover:text-blue-500 transition-colors" :icon="isExpanded2 ? ArrowUp : ArrowDown" size="small">
                  {{ isExpanded2 ? '收起' : '展开' }}
                </el-button>
                <!-- 甲板 -->
                <el-form-item label="甲板" class="mb-0">
                  <el-select v-model="searchForm.features.甲板" placeholder="请选择甲板类型" clearable class="w-full">
                    <el-option label="骨材外翻" value="骨材外翻" />
                    <el-option label="凸甲板" value="凸甲板" />
                    <el-option label="大开口" value="大开口" />
                  </el-select>
                </el-form-item>
                <!-- 壳体 -->
                <el-form-item label="壳体" class="mb-0">
                  <el-select v-model="searchForm.features.壳体" placeholder="请选择壳体类型" clearable class="w-full">
                    <el-option label="双舷侧(双壳)" value="双舷侧(双壳)" />
                    <el-option label="单舷侧(单壳)" value="单舷侧(单壳)" />
                  </el-select>
                </el-form-item>

                <!-- 舷缘 -->
                <el-form-item label="舷缘" class="mb-0">
                  <el-select v-model="searchForm.features.舷缘" placeholder="请选择舷缘类型" clearable class="w-full">
                    <el-option label="圆弧型" value="圆弧型" />
                    <el-option label="焊接型" value="焊接型" />
                  </el-select>
                </el-form-item>
              </div>
              <el-collapse-transition>
                <div v-show="isExpanded2" class="grid grid-cols-3 relative pr-[65px]">
                  <!-- 船底 -->
                  <el-form-item label="船底" class="mb-0">
                    <el-select v-model="searchForm.features.船底" placeholder="请选择船底类型" clearable class="w-full">
                      <el-option label="双层底" value="双层底" />
                      <el-option label="单层底" value="单层底" />
                    </el-select>
                  </el-form-item>
                  <!-- 纵舱壁 -->
                  <el-form-item label="纵舱壁" class="mb-0 md:col-span-2 lg:col-span-1">
                    <el-select v-model="searchForm.features.纵舱壁" placeholder="请选择纵舱壁类型" clearable class="w-full">
                      <el-option label="无纵舱壁" value="无纵舱壁" />
                      <el-option label="单纵舱壁" value="单纵舱壁" />
                      <el-option label="双纵舱壁" value="双纵舱壁" />
                    </el-select>
                  </el-form-item>
                  <!-- 是否有横撑（仅当选择双纵舱壁时显示） -->
                  <el-form-item v-show="searchForm.features.纵舱壁 === '双纵舱壁'" label="是否有横撑" class="mb-0">
                    <el-select v-model="searchForm.features.是否有横撑" placeholder="请选择" clearable class="w-full">
                      <el-option label="是" value="是" />
                      <el-option label="否" value="否" />
                    </el-select>
                  </el-form-item>

                  <!-- 边舱 -->
                  <el-form-item label="边舱" class="mb-0 md:col-span-2 lg:col-span-1">
                    <el-select v-model="searchForm.features.边舱" placeholder="请选择边舱类型" clearable class="w-full">
                      <el-option label="顶边舱" value="顶边舱" />
                      <el-option label="底边舱" value="底边舱" />
                    </el-select>
                  </el-form-item>

                  <!-- 船底骨架 -->
                  <el-form-item label="船底骨架" class="mb-0 md:col-span-2 lg:col-span-1">
                    <el-select v-model="searchForm.features.船底骨架" placeholder="请选择船底骨架类型" clearable class="w-full">
                      <el-option label="横骨架式船底" value="横骨架式船底" />
                      <el-option label="纵骨架式船底" value="纵骨架式船底" />
                    </el-select>
                  </el-form-item>

                  <!-- 舷侧骨架 -->
                  <el-form-item label="舷侧骨架" class="mb-0 md:col-span-2 lg:col-span-1">
                    <el-select v-model="searchForm.features.舷侧骨架" placeholder="请选择舷侧骨架类型" clearable class="w-full">
                      <el-option label="横骨架式舷侧" value="横骨架式舷侧" />
                      <el-option label="纵骨架式舷侧" value="纵骨架式舷侧" />
                    </el-select>
                  </el-form-item>

                  <!-- 甲板骨架 -->
                  <el-form-item label="甲板骨架" class="mb-0 md:col-span-2 lg:col-span-1">
                    <el-select v-model="searchForm.features.甲板骨架" placeholder="请选择甲板骨架类型" clearable class="w-full">
                      <el-option label="横骨架式甲板" value="横骨架式甲板" />
                      <el-option label="纵骨架式甲板" value="纵骨架式甲板" />
                    </el-select>
                  </el-form-item>

                  <!-- 舱壁类型 -->
                  <el-form-item label="舱壁" class="mb-0 md:col-span-2 lg:col-span-1">
                    <el-select v-model="searchForm.features.舱壁" placeholder="请选择舱壁类型" clearable class="w-full">
                      <el-option label="垂直槽型" value="垂直槽型" />
                      <el-option label="水平槽型" value="水平槽型" />
                    </el-select>
                  </el-form-item>

                  <!-- 舱壁结构 -->
                  <el-form-item label="舱壁结构" class="mb-0 md:col-span-2 lg:col-span-1">
                    <el-select v-model="searchForm.features.舱壁结构" placeholder="请选择舱壁结构" clearable class="w-full">
                      <el-option label="平面舱壁" value="平面舱壁" />
                      <el-option label="双层板舱壁" value="双层板舱壁" />
                      <el-option label="制荡舱壁" value="制荡舱壁" />
                    </el-select>
                  </el-form-item>

                  <!-- 底凳（仅当选择槽型舱壁时显示） -->
                  <el-form-item v-show="searchForm.features.舱壁 === '垂直槽型' || searchForm.features.舱壁 === '水平槽型'" label="底凳" class="mb-0">
                    <el-select v-model="searchForm.features.底凳" placeholder="请选择" clearable class="w-full">
                      <el-option label="有底凳" value="有底凳" />
                      <el-option label="无底凳" value="无底凳" />
                    </el-select>
                  </el-form-item>

                  <!-- 顶凳（仅当选择槽型舱壁时显示） -->
                  <el-form-item v-show="searchForm.features.舱壁 === '垂直槽型' || searchForm.features.舱壁 === '水平槽型'" label="顶凳" class="mb-0">
                    <el-select v-model="searchForm.features.顶凳" placeholder="请选择" clearable class="w-full">
                      <el-option label="有顶凳" value="有顶凳" />
                      <el-option label="无顶凳" value="无顶凳" />
                    </el-select>
                  </el-form-item>
                  <!-- 球鼻艏 -->
                  <el-form-item label="球鼻艏" class="mb-0 md:col-span-2 lg:col-span-1">
                    <el-select v-model="searchForm.features.球鼻艏" placeholder="请选择" clearable class="w-full">
                      <el-option label="有球鼻艏" value="有球鼻艏" />
                      <el-option label="无球鼻艏" value="无球鼻艏" />
                    </el-select>
                  </el-form-item>
                  <!-- 船型 -->
                  <el-form-item label="船型" class="mb-0 md:col-span-2 lg:col-span-1">
                    <el-select v-model="searchForm.features.船型" placeholder="请选择船型" clearable class="w-full">
                      <el-option label="单体" value="单体" />
                      <el-option label="双体" value="双体" />
                      <el-option label="三体" value="三体" />
                      <el-option label="顶推链接" value="顶推链接" />
                      <el-option label="箱型船体" value="箱型船体" />
                    </el-select>
                  </el-form-item>
                  <!-- 上建 -->
                  <el-form-item label="上建" class="mb-0 col-span-2">
                    <el-checkbox-group v-model="searchForm.features.上建" class="w-full flex flex-wrap gap-2">
                      <el-checkbox label="艏楼" value="艏楼" />
                      <el-checkbox label="艉楼" value="艉楼" />
                      <el-checkbox label="桥楼" value="桥楼" />
                      <el-checkbox label="长上建" value="长上建" />
                      <el-checkbox label="挡浪板" value="挡浪板" />
                    </el-checkbox-group>
                  </el-form-item>
                  <!-- 端部加强 -->
                  <el-form-item label="端部加强" class="mb-0 col-span-2">
                    <el-checkbox-group v-model="searchForm.features.端部加强" class="w-full flex flex-wrap gap-2">
                      <el-checkbox label="船端加强" value="船端加强" />
                      <el-checkbox label="尾部船底加强" value="尾部船底加强" />
                      <el-checkbox label="艏部外漂" value="艏部外漂" />
                    </el-checkbox-group>
                  </el-form-item>
                  <!-- 设备 -->
                  <el-form-item label="设备" class="mb-0 col-span-2">
                    <el-checkbox-group v-model="searchForm.features.设备" class="w-full flex flex-wrap gap-2">
                      <el-checkbox label="直升机甲板" value="直升机甲板" />
                      <el-checkbox label="车辆甲板" value="车辆甲板" />
                      <el-checkbox label="车辆跳板" value="车辆跳板" />
                    </el-checkbox-group>
                  </el-form-item>
                  <!-- 装载 -->
                  <el-form-item label="装载" class="mb-0 col-span-2">
                    <el-checkbox-group v-model="searchForm.features.装载" class="w-full flex flex-wrap gap-2">
                      <el-checkbox label="重压载舱" value="重压载舱" />
                      <el-checkbox label="独立式液舱" value="独立式液舱" />
                      <el-checkbox label="整体式液舱" value="整体式液舱" />
                      <el-checkbox label="散货舱进水" value="散货舱进水" />
                    </el-checkbox-group>
                  </el-form-item>
                </div>
              </el-collapse-transition>
            </div>
          </div>

          <!-- 第三行：功能附加 -->
          <div class="border-b border-gray-200 flex items-start mb-1 flex-col">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center m-0 w-[100px] flex-shrink-0 mb-2">
              <span class="w-1 h-6 bg-green-500 mr-3 rounded-sm"></span>
              功能附加
            </h3>
            <div class="flex-1 w-full">
              <div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-1 xl:grid-cols-1 relative pr-[65px]">
                <el-button @click="toggleExpanded3" link class="absolute top-0 right-2 text-gray-500 hover:text-blue-500 transition-colors" :icon="isExpanded3 ? ArrowUp : ArrowDown" size="small">
                  {{ isExpanded3 ? '收起' : '展开' }}
                </el-button>
                <el-form-item label="自动控制" class="mb-0 md:col-span-1 lg:col-span-1 xl:col-span-1">
                  <el-checkbox-group v-model="searchForm.features.自动控制" class="w-full flex flex-wrap gap-2">
                    <el-checkbox label="机器处所周期无人值班" value="AUT-0" />
                    <el-checkbox label="机器处所集中控制" value="MCC" />
                    <el-checkbox label="驾驶室遥控" value="BRC" />
                  </el-checkbox-group>
                </el-form-item>
              </div>

              <el-collapse-transition>
                <div v-show="isExpanded3" class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 relative pr-[65px]">
                  <!-- 自动控制 -->
                  <!-- 特殊检验 -->
                  <el-form-item label="特殊检验" class="mb-0 col-span-4">
                    <el-checkbox-group v-model="searchForm.features.特殊检验" class="w-full flex flex-wrap gap-2">
                      <el-checkbox label="加强检验程序" value="ESP" />
                      <el-checkbox label="水下检验" value="In-Water Survey" />
                      <el-checkbox label="船体循环检验" value="CHS" />
                      <el-checkbox label="轮机循环检验" value="CMS" />
                      <el-checkbox label="螺旋桨轴状态监控" value="SCM" />
                      <el-checkbox label="柴油机滑油状态监控" value="ECM" />
                      <el-checkbox label="机械计划保养系统" value="PMS" />
                      <el-checkbox label="衍射时差技术检测" value="TOFD" />
                      <el-checkbox label="相控阵超声检测对接焊缝" value="PAUT-Butt" />
                      <el-checkbox label="相控阵超声检测角焊缝" value="PAUT-Fillet" />
                      <el-checkbox label="船舶数字化检验" value="DDV" />
                    </el-checkbox-group>
                  </el-form-item>
                  <!-- 货物冷藏 -->
                  <el-form-item label="货物冷藏" class="mb-0 col-span-4">
                    <el-checkbox-group v-model="searchForm.features.货物冷藏" class="w-full flex flex-wrap gap-2">
                      <el-checkbox label="水果保鲜" value="CF" />
                      <el-checkbox label="速冻" value="QF" />
                      <el-checkbox label="舱内载运冷藏集装箱" value="CRC" />
                      <el-checkbox label="舱内载运冷藏集装箱" value="AC f/WC" />
                    </el-checkbox-group>
                  </el-form-item>

                  <!-- 其他 -->
                  <el-form-item label="其他" class="mb-0 col-span-4">
                    <el-checkbox-group v-model="searchForm.features.其他" class="w-full flex flex-wrap gap-2">
                      <el-checkbox label="船舶能效实时在线综合监控" value="EOM" />
                      <el-checkbox label="船体检查保养计划" value="HIMS" />
                      <el-checkbox label="海员起居舱室" value="Crew Accommodation(MLC)" />
                    </el-checkbox-group>
                  </el-form-item>
                </div>
              </el-collapse-transition>
            </div>
          </div>
          <!-- 操作按钮 -->
          <div class="flex justify-center border-t border-gray-200">
            <el-button type="primary" @click="handleSearch" :icon="Search" size="small" class="px-8">搜索</el-button>
            <el-button @click="handleReset" :icon="Refresh" size="small" class="px-8">重置</el-button>
          </div>
        </el-form>
      </div>
      <el-divider></el-divider>
      <!-- 船舶列表 -->
      <div>
        <div class="flex justify-end mb-2">
          <el-button @click="uploadData" size="small" class="mr-[12px]" type="primary">导出数据</el-button>
          <!-- 列显示控制 -->
          <el-dropdown trigger="click" placement="bottom-end">
            <el-button size="small" type="primary">
              <el-icon class="mr-1"><Setting /></el-icon>
              列显示设置
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <div class="p-3 min-w-48">
                  <div class="text-sm font-medium text-gray-700 mb-2">选择显示列</div>
                  <div class="grid grid-cols-3 gap-2 max-h-80 overflow-y-auto">
                    <el-checkbox v-model="columnVisible.shipLoa" size="small">总长LOA</el-checkbox>
                    <el-checkbox v-model="columnVisible.shipLpp" size="small">垂线间长</el-checkbox>
                    <el-checkbox v-model="columnVisible.shipLrule" size="small">规范船长</el-checkbox>
                    <el-checkbox v-model="columnVisible.beamArch" size="small">梁拱</el-checkbox>
                    <el-checkbox v-model="columnVisible.moldDepth" size="small">型深</el-checkbox>
                    <el-checkbox v-model="columnVisible.moldWidth" size="small">型宽</el-checkbox>
                    <el-checkbox v-model="columnVisible.structDraft" size="small">结构吃水</el-checkbox>
                    <el-checkbox v-model="columnVisible.designDraft" size="small">设计吃水</el-checkbox>
                    <el-checkbox v-model="columnVisible.dySwbmHogg" size="small">航行中拱</el-checkbox>
                    <el-checkbox v-model="columnVisible.dySwbmSagg" size="small">航行中垂</el-checkbox>
                    <el-checkbox v-model="columnVisible.staSwbmHogg" size="small">港内中拱</el-checkbox>
                    <el-checkbox v-model="columnVisible.staSwbmSagg" size="small">港内中垂</el-checkbox>
                    <el-checkbox v-model="columnVisible.dwt" size="small">排水量</el-checkbox>
                    <el-checkbox v-model="columnVisible.lsw" size="small">空船重量</el-checkbox>
                    <el-checkbox v-model="columnVisible.ndw" size="small">净载重量</el-checkbox>
                    <el-checkbox v-model="columnVisible.csa" size="small">结构设备符号</el-checkbox>
                    <el-checkbox v-model="columnVisible.csm" size="small">机械电气符号</el-checkbox>
                    <el-checkbox v-model="columnVisible.keelDate" size="small">铺龙骨日期</el-checkbox>
                  </div>
                  <div class="mt-3 pt-2 border-t border-gray-200">
                    <el-button size="small" type="primary" @click="resetColumns">显示全部</el-button>
                  </div>
                </div>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <el-table :data="shipList" v-loading="loading" stripe style="width: 100%" max-height="300px" empty-text="暂无数据" show-overflow-tooltip>
          <!-- 基本信息列 -->
          <el-table-column prop="shipName" label="船名" width="70" fixed="left" />
          <el-table-column prop="shipOwner" label="船东" width="70" />
          <el-table-column prop="shipType" label="船舶类型" width="80" />
          <el-table-column prop="area" label="航区" width="70" />
          <el-table-column prop="shipYard" label="船厂" width="70" />
          <el-table-column prop="imo" label="IMO号" width="70" />

          <!-- 可选显示的列 -->
          <el-table-column v-if="columnVisible.shipLoa" prop="shipLoa" label="总长LOA(m)" width="110" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.shipLoa ? Number(scope.row.shipLoa).toFixed(2) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.shipLpp" prop="shipLpp" label="垂线间长LPP(m)" width="120" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.shipLpp ? Number(scope.row.shipLpp).toFixed(2) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.shipLrule" prop="shipLrule" label="规范船长(m)" width="120" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.shipLrule ? Number(scope.row.shipLrule).toFixed(2) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.beamArch" prop="beamArch" label="梁拱(m)" width="70" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.beamArch ? Number(scope.row.beamArch).toFixed(2) : '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column v-if="columnVisible.moldDepth" prop="moldDepth" label="型深(m)" width="70" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.moldDepth ? Number(scope.row.moldDepth).toFixed(2) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.moldWidth" prop="moldWidth" label="型宽(m)" width="70" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.moldWidth ? Number(scope.row.moldWidth).toFixed(2) : '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column v-if="columnVisible.structDraft" prop="structDraft" label="结构吃水(m)" width="110" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.structDraft ? Number(scope.row.structDraft).toFixed(2) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.designDraft" prop="designDraft" label="设计吃水(m)" width="110" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.designDraft ? Number(scope.row.designDraft).toFixed(2) : '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column v-if="columnVisible.dySwbmHogg" prop="dySwbmHogg" label="航行中拱(kNm)" width="110" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.dySwbmHogg ? Number(scope.row.dySwbmHogg).toFixed(0) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.dySwbmSagg" prop="dySwbmSagg" label="航行中垂(kNm)" width="110" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.dySwbmSagg ? Number(scope.row.dySwbmSagg).toFixed(0) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.staSwbmHogg" prop="staSwbmHogg" label="港内中拱(kNm)" width="110" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.staSwbmHogg ? Number(scope.row.staSwbmHogg).toFixed(0) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.staSwbmSagg" prop="staSwbmSagg" label="港内中垂(kNm)" width="110" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.staSwbmSagg ? Number(scope.row.staSwbmSagg).toFixed(0) : '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column v-if="columnVisible.dwt" prop="dwt" label="排水量(t)" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.dwt ? Number(scope.row.dwt).toFixed(0) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.lsw" prop="lsw" label="空船重量(t)" width="110" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.lsw ? Number(scope.row.lsw).toFixed(0) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.ndw" prop="ndw" label="净载重量(t)" width="110" class-name="number-column">
            <template #default="scope">
              <span class="number-value">{{ scope.row.ndw ? Number(scope.row.ndw).toFixed(0) : '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column v-if="columnVisible.csa" prop="csa" label="结构设备符号" width="110">
            <template #default="scope">
              {{ scope.row.csa || '-' }}
            </template>
          </el-table-column>
          <el-table-column v-if="columnVisible.csm" prop="csm" label="机械电气符号" width="110">
            <template #default="scope">
              {{ scope.row.csm || '-' }}
            </template>
          </el-table-column>

          <el-table-column v-if="columnVisible.keelDate" prop="keelDate" label="铺龙骨日期" width="120">
            <template #default="scope">
              {{ scope.row.keelDate || '-' }}
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column fixed="right" label="操作" width="80">
            <template #default="scope">
              <el-button link type="primary" size="small" @click="handleViewDetails(scope.row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="flex justify-end items-end mt-6 flex-col-reverse">
          <el-pagination
            v-model:current-page="currentPage"
            :page-size="20"
            :small="false"
            :disabled="loading"
            :background="true"
            layout="prev, pager, next, jumper,total"
            :total="total"
            :pager-count="7"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Search, Refresh, ArrowUp, ArrowDown, Setting } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import Apis from '@/apis'
import RangeInput from '@/components/RangeInput/RangeInput.vue'
import _ from 'lodash'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const shipList = ref([])
const currentPage = ref(1)
const total = ref(0)

// 展开/收起状态
const isExpanded = ref(false)
const isExpanded2 = ref(false)
const isExpanded3 = ref(false)

// 搜索表单
const searchForm = ref({
  // 基本信息
  shipName: '',
  shipOwner: '',
  shipType: '',
  area: '',
  shipYard: '',
  imo: '',
  minShipLoa: '',
  maxShipLoa: '',
  minDwt: '',
  maxDwt: '',
  minMoldWidth: '',
  maxMoldWidth: '',
  minMoldDepth: '',
  maxMoldDepth: '',
  minStructDraft: '',
  maxStructDraft: '',
  minDesignDraft: '',
  maxDesignDraft: '',
  csa: '',
  csm: '',
  // 结构特征和功能附加
  features: {
    // 结构特征
    甲板: '',
    壳体: '',
    舷缘: '',
    船底: '',
    纵舱壁: '',
    是否有横撑: '',
    边舱: '',
    船底骨架: '',
    舷侧骨架: '',
    甲板骨架: '',
    舱壁: '',
    舱壁结构: '',
    底凳: '',
    顶凳: '',
    球鼻艏: '',
    船型: '',
    上建: [],
    端部加强: [],
    装载: [],
    设备: [],
    // 功能附加
    自动控制: [],
    特殊检验: [],
    货物冷藏: [],
    其他: []
  }
})
const storedFilterInfo = ref(Object.assign({}, searchForm.value))
const uploadData = async () => {
  const res = await Apis.general.post_shipstruct_ship_admin_export({
    data: {
      ...storedFilterInfo.value
    }
  })
  if (res && res.code == 200) {
    // ElMessage.success('导出成功，请在导出记录中查看')
  }
}
// 列显示控制
const columnVisible = ref({
  shipLoa: true,
  shipLpp: true,
  shipLrule: true,
  beamArch: true,
  moldDepth: true,
  moldWidth: true,
  structDraft: false,
  designDraft: false,
  dySwbmHogg: false,
  dySwbmSagg: false,
  staSwbmHogg: false,
  staSwbmSagg: false,
  dwt: true,
  lsw: false,
  ndw: false,
  csa: false,
  csm: false,
  keelDate: false
})

// 获取船舶列表数据
const fetchShipList = async () => {
  loading.value = true
  try {
    const params = {
      pageNo: currentPage.value.toString()
    }
    const features = _.cloneDeep(searchForm.value.features)
    for (let key in features) {
      if (Array.isArray(features[key])) {
        features[key] = features[key].join(',')
      }
      if (features[key] === '' || features[key] === null) {
        delete features[key]
      }
    }
    const response = await Apis.general.post_shipstruct_ship_struct_getallships({
      params,
      data: {
        ...searchForm.value,
        features
      }
    })

    if (response.code === 200) {
      shipList.value = response.data?.rows || []
      total.value = response.data?.total || 0
      storedFilterInfo.value = Object.assign({}, { ...searchForm.value, features })
    } else {
      console.error('获取船舶列表失败:', response.msg)
      shipList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取船舶列表失败:', error)
    shipList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchShipList()
}

const handleReset = () => {
  // 重置基本信息
  searchForm.value.shipName = ''
  searchForm.value.shipOwner = ''
  searchForm.value.shipType = ''
  searchForm.value.area = ''
  searchForm.value.shipYard = ''
  searchForm.value.imo = ''
  searchForm.value.minShipLoa = ''
  searchForm.value.maxShipLoa = ''
  searchForm.value.minDwt = ''
  searchForm.value.maxDwt = ''
  searchForm.value.minMoldWidth = ''
  searchForm.value.maxMoldWidth = ''
  searchForm.value.minMoldDepth = ''
  searchForm.value.maxMoldDepth = ''
  searchForm.value.minStructDraft = ''
  searchForm.value.maxStructDraft = ''
  searchForm.value.minDesignDraft = ''
  searchForm.value.maxDesignDraft = ''
  searchForm.value.csa = ''
  searchForm.value.csm = ''

  // 重置features对象
  searchForm.value.features = {
    // 结构特征
    甲板: '',
    壳体: '',
    舷缘: '',
    船底: '',
    纵舱壁: '',
    是否有横撑: '',
    边舱: '',
    船底骨架: '',
    舷侧骨架: '',
    甲板骨架: '',
    舱壁: '',
    舱壁结构: '',
    底凳: '',
    顶凳: '',
    球鼻艏: '',
    船型: '',
    上建: [],
    端部加强: [],
    装载: [],
    设备: [],
    // 功能附加
    自动控制: [],
    特殊检验: [],
    货物冷藏: [],
    其他: []
  }

  currentPage.value = 1
  fetchShipList()
}

const handleCurrentChange = val => {
  currentPage.value = val
  fetchShipList()
}

const handleViewDetails = row => {
  router.push({
    name: 'shipDetail',
    params: { id: row.id }
  })
}

// 切换展开/收起状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

const toggleExpanded2 = () => {
  isExpanded2.value = !isExpanded2.value
}

const toggleExpanded3 = () => {
  isExpanded3.value = !isExpanded3.value
}

// 重置列显示为默认
const resetColumns = () => {
  columnVisible.value = {
    shipLoa: true,
    shipLpp: true,
    shipLrule: true,
    beamArch: true,
    moldDepth: true,
    moldWidth: true,
    structDraft: true,
    designDraft: true,
    dySwbmHogg: true,
    dySwbmSagg: true,
    staSwbmHogg: true,
    staSwbmSagg: true,
    dwt: true,
    lsw: true,
    ndw: true,
    csa: true,
    csm: true,
    keelDate: true
  }
  fetchShipList() // 重新加载数据以应用新的列显示
}

onMounted(() => {
  fetchShipList()
})
</script>

<style scoped>
:deep(.el-form-item) {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}
</style>
