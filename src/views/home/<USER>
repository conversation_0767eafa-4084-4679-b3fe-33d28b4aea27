<template>
  <div class="p-5 pt-0">
    <div class="max-w-7xl mx-auto">
      <!-- 顶部用户信息栏 -->
      <el-card class="mb-8" shadow="never">
        <div class="flex justify-between items-center">
          <div class="flex items-center gap-5">
            <div class="flex-1">
              <div class="flex items-center gap-3 mb-2">
                <h3 class="text-xl font-bold text-gray-800 m-0">
                  {{ userInfo.userName }}
                </h3>
                <el-tag size="small" type="primary" effect="light" v-if="userInfo.email">{{ userInfo.email }}</el-tag>
              </div>
              <div class="space-y-1">
                <div class="flex items-center gap-2 text-sm" v-if="userInfo.comGroup">
                  <span class="text-gray-700 font-medium flex items-center gap-1" title="点击编辑工作单位信息">
                    {{ userInfo.comGroup || 'shuiyanyuan' }}
                  </span>
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-500">
                  <span>最后登录：{{ formatLoginTime }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="flex items-center gap-4">
            <div class="flex items-center gap-3">
              <div class="flex items-center gap-2 text-gray-600">
                <span class="w-2 h-2 bg-green-500 rounded-full shadow-lg shadow-green-500/50 animate-pulse"></span>
                <span class="text-sm">系统正常</span>
              </div>
              <div class="font-mono text-gray-800 font-medium text-sm">
                {{ currentTime }}
              </div>
            </div>
          </div>
        </div>
      </el-card>
      <!-- 统计卡片区域 -->
      <el-card class="mb-8" header="船舶统计" shadow="never">
        <div class="grid gap-5 grid-flow-col" v-loading="statisticsLoading">
          <!-- 总数量卡片 -->
          <el-card class="min-w-[200px] h-[110px]" shadow="never">
            <div class="flex items-center gap-5">
              <div class="w-15 h-15 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white text-2xl">
                <i class="el-icon-ship"></i>
              </div>
              <div class="flex-1">
                <h3 class="text-3xl font-bold text-gray-800 m-0 mb-2">
                  {{ totalShips }}
                </h3>
                <p class="text-gray-600 text-sm m-0 mb-2">船舶总数量</p>
              </div>
            </div>
          </el-card>
          <!-- 动态船舶类型卡片 -->
          <el-card v-for="(item, index) in shipTypeStats" :key="item.ship_type" class="min-w-[200px] h-[110px]" shadow="never">
            <div class="flex items-center gap-5">
              <div :class="['w-15 h-15 rounded-xl flex items-center justify-center text-white text-2xl', getGradientClass(index)]">
                <i :class="getIconClass(item.ship_type)"></i>
              </div>
              <div class="flex-1">
                <h3 class="text-3xl font-bold text-gray-800 m-0 mb-2">
                  {{ item.cnt }}
                </h3>
                <p class="text-gray-600 text-sm m-0 mb-2">{{ item.ship_type }}</p>
              </div>
            </div>
          </el-card>
        </div>
      </el-card>
      <el-card class="mb-8" header="最新船舶信息" v-loading="latestShipLoading" shadow="never">
        <div class="text-sm font-semibold text-gray-800 flex items-center w-[100px] flex-shrink-0 mb-3">
          <span class="w-1 h-6 bg-blue-500 mr-3 rounded-sm"></span>
          基本信息
        </div>
        <div class="grid grid-cols-3 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-5 gap-4 mb-6">
          <template v-for="(item, key) in latestShipInfo.ship" :key="key">
            <custom-tag v-if="getLabel(shipInfoMap, key)" :label="getLabel(shipInfoMap, key)" :value="item" type="success" />
          </template>
        </div>
        <div class="text-sm font-semibold text-gray-800 flex items-center w-[100px] flex-shrink-0 mb-3">
          <span class="w-1 h-6 bg-blue-500 mr-3 rounded-sm"></span>
          报告
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4 mb-6">
          <el-card v-for="drawing in latestShipInfo.report" :key="drawing.id" class="p-2" shadow="never">
            <PdfCanvas :maxPage="1" class="w-full h-[200px]" :url="drawing.filePath" />
            <h4 class="font-semibold text-gray-900 m-2">{{ drawing.subTitle }}</h4>
          </el-card>
        </div>
        <!-- <div class="text-sm font-semibold text-gray-800 flex items-center w-[100px] flex-shrink-0 mb-3">
          <span class="w-1 h-6 bg-blue-500 mr-3 rounded-sm"></span>
          许用值
        </div>
        <div>
          <el-descriptions>
            <el-descriptions-item label="Username">kooriookami</el-descriptions-item>
          </el-descriptions>
        </div> -->
      </el-card>
      <!-- 快速操作区域 -->
      <el-card header="快速操作" shadow="never">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
          <div v-for="item in quickActions" :key="item.key" class="cursor-pointer" @click="handleQuickAction(item.action)">
            <div class="flex items-center gap-4">
              <div :class="['w-11 h-11 rounded-lg flex items-center justify-center text-white text-xl', item.bgClass]">
                <i :class="item.icon"></i>
              </div>
              <div>
                <h4 class="text-base font-semibold text-gray-800 m-0 mb-1">{{ item.title }}</h4>
                <p class="text-sm text-gray-600 m-0">{{ item.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed } from 'vue'
import { Edit } from '@element-plus/icons-vue'
import useUserStore from '@/store/modules/user'
import moment from 'moment'
import Apis from '@/apis'
import { useRouter } from 'vue-router'
import CustomTag from '@/components/CustomTag/CustomTag.vue'
import PdfCanvas from '@/components/PdfCanvas/index.vue'
const router = useRouter()

const userInfo = useUserStore()

// 统计数据
const shipTypeStats = ref([])
const totalShips = ref(0)
const statisticsLoading = ref(false)

// 最新船舶信息
const latestShipInfo = ref({
  ship: {},
  report: [],
  allowStress: []
})
const latestShipLoading = ref(false)

const currentTime = ref('')

// 快速操作配置
const quickActions = ref([
  {
    key: 'query',
    title: '船舶查询',
    description: '快速查询船舶信息',
    icon: 'el-icon-search',
    bgClass: 'bg-gradient-to-br from-blue-500 to-blue-600',
    action: 'query'
  },
  {
    key: 'apply',
    title: '数据申请',
    description: '申请数据访问权限',
    icon: 'el-icon-document-add',
    bgClass: 'bg-gradient-to-br from-green-500 to-green-600',
    action: 'apply'
  },
  {
    key: 'input',
    title: '数据录入',
    description: '录入新的船舶数据',
    icon: 'el-icon-edit',
    bgClass: 'bg-gradient-to-br from-yellow-500 to-yellow-600',
    action: 'input'
  },
  {
    key: 'export',
    title: '数据导出',
    description: '导出分析报告',
    icon: 'el-icon-download',
    bgClass: 'bg-gradient-to-br from-purple-500 to-purple-600',
    action: 'export'
  }
])

// 计算属性
const formatLoginTime = computed(() => {
  return moment(userInfo.loginTime).format('YYYY-MM-DD HH:mm:ss')
})

// 获取船舶类型统计数据
const fetchShipTypeStats = async () => {
  try {
    statisticsLoading.value = true
    const response = await Apis.general.post_shipstruct_ship_struct_statisbytype()
    console.log(response, 'res')
    if (response.code === 200) {
      shipTypeStats.value = response.data || []
      // 计算总数量
      totalShips.value = shipTypeStats.value.length ? shipTypeStats.value.reduce((sum, item) => sum + (item.cnt || 0), 0) : 0
    }
  } catch (error) {
    console.error('API调用失败:', error)
    // 使用默认数据
    shipTypeStats.value = []
    totalShips.value = 0
  } finally {
    statisticsLoading.value = false
  }
}

// 获取最新船舶信息
const fetchLatestShipInfo = async () => {
  try {
    latestShipLoading.value = true
    const response = await Apis.general.get_shipstruct_ship_getlatestship()
    if (response.code === 200 && response.data) {
      latestShipInfo.value = {
        ship: response.data.ship || {},
        report: response.data.report || [],
        allowStress: response.data.allowStress || []
      }
      console.log(latestShipInfo.value, 'vv')
    }
  } catch (error) {
    console.error('获取最新船舶信息失败:', error)
    // 使用默认空数据
    latestShipInfo.value = {
      ship: {},
      report: [],
      allowStress: []
    }
  } finally {
    latestShipLoading.value = false
  }
}

// 获取百分比
const getPercentage = count => {
  if (totalShips.value === 0) return 0
  return ((count / totalShips.value) * 100).toFixed(1)
}

// 根据索引获取渐变类
const getGradientClass = index => {
  const gradients = [
    'bg-gradient-to-br from-green-500 to-green-600',
    'bg-gradient-to-br from-yellow-500 to-yellow-600',
    'bg-gradient-to-br from-red-500 to-red-600',
    'bg-gradient-to-br from-purple-500 to-purple-600',
    'bg-gradient-to-br from-cyan-500 to-cyan-600',
    'bg-gradient-to-br from-pink-500 to-pink-600',
    'bg-gradient-to-br from-indigo-500 to-indigo-600',
    'bg-gradient-to-br from-orange-500 to-orange-600'
  ]
  return gradients[index % gradients.length]
}

// 根据船舶类型获取图标类
const getIconClass = shipType => {
  const iconMap = {
    油船: 'el-icon-oil-barrel',
    散货船: 'el-icon-box',
    挖泥船: 'el-icon-construction',
    集装箱船: 'el-icon-goods',
    客船: 'el-icon-user',
    货船: 'el-icon-truck',
    渔船: 'el-icon-fishing',
    拖船: 'el-icon-connection',
    工程船: 'el-icon-tools',
    科考船: 'el-icon-telescope'
  }
  return iconMap[shipType] || 'el-icon-ship'
}

// 方法
const updateCurrentTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

const handleQuickAction = action => {
  const routeMap = {
    query: 'shipList',
    apply: 'dataApplication',
    input: 'dataImport',
    export: 'dataExport'
  }
  const routeName = routeMap[action]
  if (routeName) {
    router.push({ name: routeName })
  }
}

const handleEditUserInfo = () => {
  // 跳转到用户信息编辑页面
  console.log('跳转到用户信息编辑页面')
  // 这里可以使用 useRouter 进行路由跳转
  // router.push('/user/profile/edit');
}
let interval = null
// 生命周期
onMounted(async () => {
  updateCurrentTime()
  interval = setInterval(updateCurrentTime, 1000)
  // 获取统计数据
  await fetchShipTypeStats()
  // 获取最新船舶信息
  await fetchLatestShipInfo()
})
onBeforeUnmount(() => {
  interval && clearInterval(interval)
})
const getLabel = (map, key) => {
  if (Object.keys(map).includes(key)) {
    return map[key]
  }
  return ''
}
const shipInfoMap = {
  shipName: '船名',
  shipOwner: '船东',
  area: '船籍',
  imo: 'IMO',
  shipLoa: '船长',
  structDraft: '结构吃水(m)',
  designDraft: '设计吃水(m)',
  shipType: '船型',
  shipYard: '船厂',
  shipLpp: '垂线间长(m)',
  shipLrule: '规范船长(m)',
  beamArch: '梁拱',
  moldDepth: '型深(m)',
  moldWidth: '型宽(m)',
  dySwbmHogg: '请输入航行工况静水弯矩-中拱(kN·m)',
  dySwbmSagg: '请输入港内工况静水弯矩-中垂(kN·m)',
  staSwbmHogg: '请输入港内工况静水弯矩-中拱(kN·m)',
  staSwbmSagg: '请输入港内静水弯矩-中垂(kN·m)',
  dwt: '排水量(t)',
  lsw: '空船重量(t)',
  ndw: '净载重量(t)',
  csa: 'CSA',
  csm: 'CSM',
  keelDate: '铺龙骨日期'
  // features: null,
  // ctime: null,
  // mtime: null
}
</script>
