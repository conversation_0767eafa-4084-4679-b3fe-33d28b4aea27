<template>
  <template v-for="(item, index) in menuList" :key="index">
    <!-- 没有子菜单的项 -->
    <el-menu-item
      v-if="!item.children || item.children.length === 0"
      :index="item.fullPath"
      @click="handleClick(item)"
    >
      <svg-icon
        v-if="item.meta && item.meta.icon"
        :name="item.meta.icon"
        :color="getIconColor(item)"
        :size="iconSize"
        class="menu-icon mr-[10px]"
      />
      <template #title>{{ item.meta.title }}</template>
    </el-menu-item>

    <!-- 有子菜单的项 -->
    <el-sub-menu v-else :index="item.fullPath">
      <template #title>
        <svg-icon
          v-if="item.meta && item.meta.icon"
          :name="item.meta.icon"
          :color="getIconColor(item)"
          :size="iconSize"
          class="menu-icon mr-[10px]"
        />
        <span>{{ item.meta.title }}</span>
      </template>

      <!-- 递归调用自身处理子菜单 -->
      <recursive-menu
        :menu-list="item.children"
        :icon-color="iconColor"
        :icon-size="iconSize"
        @menu-click="handleClick"
      />
    </el-sub-menu>
  </template>
</template>

<script setup>
import { defineProps, defineEmits } from "vue";
import SvgIcon from "@/components/common/SvgIcon.vue";

const props = defineProps({
  menuList: {
    type: Array,
    required: true,
  },
  // 默认图标颜色
  iconColor: {
    type: String,
    default: "currentColor",
  },
  // 默认图标尺寸
  iconSize: {
    type: String,
    default: "16px",
  },
});

const emit = defineEmits(["menu-click"]);

const handleClick = (menu) => {
  emit("menu-click", menu);
};

// 获取图标颜色，优先使用菜单项自己的颜色设置，否则使用全局颜色
const getIconColor = (item) => {
  return item.meta?.iconColor || props.iconColor;
};
</script>

<style scoped>
.menu-icon {
  margin-right: 5px;
}
</style>
