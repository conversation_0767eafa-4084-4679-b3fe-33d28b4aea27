<template>
  <div class="loading-container">
    <div class="spinner">
      <div class="double-bounce1"></div>
      <div class="double-bounce2"></div>
    </div>
    <div class="loading-text" v-if="text">{{ text }}</div>
  </div>
</template>

<script setup>
defineProps({
  text: {
    type: String,
    default: "加载中...",
  },
  color: {
    type: String,
    default: "#1890ff",
  },
  size: {
    type: Number,
    default: 40,
  },
});
</script>

<style lang="scss" scoped>
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.spinner {
  position: relative;
  width: v-bind('size + "px"');
  height: v-bind('size + "px"');
  margin-bottom: 10px;
}

.double-bounce1,
.double-bounce2 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: v-bind(color);
  opacity: 0.6;
  animation: bounce 2s infinite ease-in-out;
}

.double-bounce2 {
  animation-delay: -1s;
}

.loading-text {
  color: v-bind(color);
  font-size: 14px;
}

@keyframes bounce {
  0%,
  100% {
    transform: scale(0);
  }
  50% {
    transform: scale(1);
  }
}
</style>
