<script name="CSSelect" setup>
import {
  defineProps,
  defineEmits,
  computed,
  ref,
  watch,
  unref,
  useAttrs,
} from "vue";
import { get } from "lodash-es";

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  valueField: {
    type: String,
    default: "value",
  },
  labelField: {
    type: String,
    default: "label",
  },
  api: {
    type: Object,
    default: null,
  },
  remote: {
    type: [Boolean, String],
    default: false,
  },
});

const attrs = useAttrs();
const innerList = ref(unref(props.list));
const filterable = ref(!!unref(attrs.filterable));
let remoteMethod = null;

// 当remote为true时，设置远程搜索方法
if (props.remote) {
  filterable.value = true;
  remoteMethod = async (query) => {
    if (!props.api || !props.api.handler) return;

    const { params, handler, path, processer } = props.api;
    try {
      const res = await handler({ ...(params ?? {}), [props.remote]: query });
      if (res && res.code === 200) {
        if (processer) {
          processer(res.data, innerList);
          return innerList.value;
        }
        if (path) {
          innerList.value = get(res.data, path);
          return innerList.value;
        }
        innerList.value = res.data;
        return innerList.value;
      }
    } catch (error) {
      console.error("远程搜索失败:", error);
      return [];
    }
  };
}

// 获取列表数据
const getList = async () => {
  if (!props.api || !props.api.handler) return;

  try {
    const { params, handler, path, processer } = props.api;
    const res = await handler(params ?? {});
    if (res && res.code === 200) {
      if (processer) {
        processer(res.data, innerList);
        return innerList.value;
      }
      if (path) {
        innerList.value = get(res.data, path);
        return innerList.value;
      }
      innerList.value = res.data;
      return innerList.value;
    }
  } catch (error) {
    console.error("获取选项列表失败:", error);
    innerList.value = [];
  }
};

// 组件初始化时获取数据
if (props.api) {
  getList();
}

// 监听列表变化
watch(
  () => props.list,
  (val) => {
    innerList.value = unref(val);
  }
);
const emit = defineEmits(["change"]);
const onChange = (val) => {
  console.log(val);
  const selectedItem = innerList.value.find(
    (item) => item[props.valueField] === val
  );
  console.log(selectedItem);
  emit("change", selectedItem, val);
};
const generatorValue = (v) => {
  // 尝试将值转换为数字
  const num = Number(v);
  // 如果转换结果是有效数字且不是NaN，返回数字类型
  return !isNaN(num) && String(num) === String(v) ? num : v;
};
</script>

<template>
  <!-- 定义子组件 -->
  <el-select
    v-bind="$attrs"
    :filterable="filterable"
    :remote="props.remote"
    :remote-method="remoteMethod"
    @change="onChange"
  >
    <el-option
      v-for="item in innerList"
      :key="item[valueField]"
      :label="item[labelField]"
      :value="generatorValue(item[valueField])"
    />
  </el-select>
</template>

<style scoped>
/* scoped 使用得定义的样式只在本页面内生效  */
</style>
