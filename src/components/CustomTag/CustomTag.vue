<template>
  <el-tooltip :content="value + (unit || '')" effect="dark" placement="top" :disabled="tooltipsDisabled">
    <div class="flex flex-col p-3 rounded-lg property-tag" :class="bgColorClass">
      <span class="text-sm font-medium opacity-75 label-text">{{ label }}</span>
      <div class="flex items-baseline gap-1.5 mt-0.5 value-container">
        <span ref="valueEl" class="text-2xl font-bold value-number">{{ value }}</span>
        <span class="text-sm opacity-75">{{ unit }}</span>
      </div>
    </div>
  </el-tooltip>
</template>

<script setup>
import { computed, ref, onMounted, nextTick } from 'vue'
import { ElTooltip } from 'element-plus'
import { useReplaceTooltip } from '@/use/useReplaceTooltip'
const props = defineProps({
  label: {
    type: String
  },
  value: {
    type: [String, Number],
    default: null
  },
  unit: {
    type: String
  },
  type: {
    type: String,
    default: 'default'
  }
})
const {
  tooltipsDisabled,
  labelEl: valueEl,
  checkDisabelStatus
} = useReplaceTooltip({
  text: props.value
})
watch(
  () => props.value,
  v => {
    checkDisabelStatus(v)
  }
)
const bgColorClass = computed(() => {
  const classes = {
    default: 'bg-gradient-to-br from-gray-50 to-gray-100 text-gray-800 border border-gray-200',
    success: 'bg-gradient-to-br from-green-50 to-green-100 text-green-800 border border-green-200',
    warning: 'bg-gradient-to-br from-orange-50 to-orange-100 text-orange-800 border border-orange-200',
    danger: 'bg-gradient-to-br from-red-50 to-red-100 text-red-800 border border-red-200',
    info: 'bg-gradient-to-br from-blue-50 to-blue-100 text-blue-800 border border-blue-200'
  }
  return classes[props.type] || classes.warning
})
</script>

<style scoped>
.matrix-tag {
  height: auto;
  padding: 8px 12px;
}

.tag-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.tag-label {
  font-size: 12px;
  opacity: 0.8;
}

.tag-value {
  font-size: 14px;
  font-weight: bold;
}

/* 优化后的样式效果 */
.property-tag {
  transition: all 0.25s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
  animation: fadeIn 0.4s ease-out;
  backdrop-filter: blur(2px);
  position: relative;
  border-left-width: 2px;
  width: 100%; /* 固定宽度 */
  transform: translateZ(0);
}

.label-text {
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.value-container {
  width: 100%;
  overflow: hidden;
}

.value-number {
  transition: color 0.2s ease;
  letter-spacing: -0.01em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100% - 20px); /* 减去单位的空间 */
  font-family: 'Inter', system-ui, sans-serif;
  font-weight: 700;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
  line-height: 1.2;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.property-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0;
  background-color: rgba(255, 255, 255, 0.1);
  transition: height 0.3s ease;
  z-index: -1;
  border-radius: 8px 8px 0 0;
}

.property-tag:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: currentColor;
  transform: translateY(-2px) scale(1.02);
}

.property-tag:hover .value-number {
  color: currentColor;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.property-tag:hover .label-text {
  opacity: 0.9;
}

.property-tag:hover::before {
  height: 100%;
  background-color: rgba(255, 255, 255, 0.2);
}

/* 值变化时的动画 - 更微妙的颜色变化 */
@keyframes softHighlight {
  0% {
    color: inherit;
  }
  40% {
    color: rgba(0, 0, 0, 0.7);
  }
  100% {
    color: inherit;
  }
}

.value-number:not(:empty) {
  animation: softHighlight 0.8s ease;
}

/* 初始加载动画 - 优化过渡效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
