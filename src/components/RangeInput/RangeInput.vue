<script name="rangeInput" setup>
const props = defineProps({
  min: {
    type: [Number, String],
    default: null,
  },
  max: {
    type: [Number, String],
    default: null,
  },
  separator: {
    type: String,
    default: "-",
  },
  prePlaceholder: {
    type: String,
    default: "",
  },
  nextPlaceholder: {
    type: String,
    default: "",
  },
  size: {
    type: String,
    default: "small",
  },
  type: {
    type: String,
    default: null,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  inputType: {
    type: String,
    default: null,
  },
});
const emit = defineEmits(["update:min", "update:max", "onBlur"]);
const changeValue = (val, type) => {
  if (type === "min") {
    emit("update:min", val);
  } else {
    emit("update:max", val);
  }
};
const blurHandler = (e, type) => {
  emit("onBlur", e, type);
};
</script>
<template>
  <div
    :class="`el-date-editor el-date-editor--datetimerange el-input__wrapper el-range-editor--${props.size} el-tooltip__trigger el-tooltip__trigger rangeInput`"
  >
    <!-- 定义子组件 -->
    <el-input
      :placeholder="props.prePlaceholder"
      class="range-input"
      :model-value="props.min"
      v-bind="$attrs"
      type="number"
      :size="props.size"
      :disabled="props.disabled"
      @input="(val) => changeValue(val, 'min')"
      @blur="(e) => blurHandler(e, 'min')"
    ></el-input>
    <span class="el-range-separator">{{ props.separator }}</span>
    <el-input
      :placeholder="props.nextPlaceholder"
      class="range-input"
      :model-value="props.max"
      v-bind="$attrs"
      type="number"
      :size="props.size"
      :disabled="props.disabled"
      @input="(val) => changeValue(val, 'max')"
      @blur="(e) => blurHandler(e, 'max')"
    ></el-input>
  </div>
  <!-- <el-date-picker type="datetimerange" start-placeholder="Start Date" end-placeholder="End Date" size="small" /> -->
</template>
<style scoped lang="scss">
/* scoped 使用得定义的样式只在本页面内生效  */
$sizes: small, default, large;
.range-input {
  ::v-deep .el-input__wrapper {
    border: none;
    box-shadow: none;
    .el-input__inner {
      text-align: center;
    }
  }
  ::v-deep .el-input--small {
    --el-input-height: calc(var(--el-component-size-small) - 2px);
    font-size: 12px;
  }
}
.range-input {
}
$sizes: small, default, large;
@each $size in $sizes {
  .el-input--#{$size} {
    --el-input-height: calc(var(--el-component-size-#{$size}) - 2px);
    --el-input-inner-height: calc(var(--el-input-height, 24px) - 2px);
    font-size: 12px;
  }
}
</style>
