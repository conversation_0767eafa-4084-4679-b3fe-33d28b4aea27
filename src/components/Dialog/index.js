import { ElButton, ElDialog, ElInput } from 'element-plus'
import useDialogStore from '@/store/modules/Dialog'
import { isVNode, cloneVNode, Fragment } from 'vue'
import { defineComponent, h, ref, useAttrs, shallowRef } from 'vue'

const DialogStack = defineComponent({
    name: 'DialogStack',
    setup() {
        const store = useDialogStore()
        return () => store.stacks.map(props => {
            return h(BaseDialog, props)
        })
    }
})
const renderFooter = (footer, onSave = () => { }, onCancel = () => { }, childRef, dialogVisible) => {
    const renderCancelBtn = (text, handler) => {
        const clickHandler = async () => {
            await handler()
            childRef?.value?.reset?.()
            dialogVisible.value = false
            const store = useDialogStore()
            store.remove()
        }
        return h(ElButton, { onClick: clickHandler, type: 'primary' }, () => text)
    }
    const renderConfirmBtn = (text, handler) => {
        const clickHandler = async () => {
            try {
                const store = useDialogStore()
                // 验证不通过会走catch,当没有验证的时候就走else逻辑
                const res = await childRef?.value?.submit?.()
                if (res) {
                    childRef?.value?.reset?.()
                    handler(childRef)
                    dialogVisible.value = false
                    store.remove()
                } else {
                    // 自定义的方法，最后返回true走关闭的逻辑
                    const res = await handler(childRef)
                    if (res) {
                        dialogVisible.value = false
                        store.remove()
                    }
                }
            } catch (err) {
                console.log(err, childRef, "err");
            }

        }
        return h(ElButton, { onClick: clickHandler, type: 'primary' }, () => text)
    }
    if (typeof footer === 'boolean') {
        return () => h('div', { class: 'flex justify-end items-center' }, [renderCancelBtn('取消', onCancel), renderConfirmBtn('确认', onSave)])
    }
    if (footer instanceof Array) {
        return () => h('div', { class: 'flex justify-end items-center' }, [footer[0] === null ? null : renderCancelBtn(footer[0], onCancel), footer[1] === null ? null : renderConfirmBtn(footer[1], onSave)])
    }
}
const BaseDialog = defineComponent({
    name: 'BaseDialog',
    inheritAttrs: false,
    setup() {
        const { component, componentProps, footer, header, onSave, onCancel, ...restProps } = useAttrs()
        const childRef = ref(null)
        const dialogVisible = ref(true)
        return () => h(ElDialog, {
            modelValue: dialogVisible.value, 'onUpdate:modelValue': (v) => dialogVisible.value = v, ...restProps, childRef, onClose: () => {
                childRef?.value?.reset?.()
            }
        },
            {
                default: () => {
                    return isComponentTemplate(component) ?
                        h(toRaw(component), { ...componentProps, ref: childRef }, { ...componentProps?.slots }) : isVNode(component) ? cloneVNode(component, { ref: childRef }) : component
                },
                footer: renderFooter(footer, onSave, onCancel, childRef, dialogVisible),
                header: typeof header === 'string' ? () => header : header?.()
            })
    }
})

const isComponentTemplate = (v) => {
    return !!(v.setup && (v.render || v.install))
}
export const useDialog = ({ component = 'div', componentProps = {}, dialogProps = {} } = {}) => {

    const modelValue = ref(false)
    const store = useDialogStore()
    const open = () => {
        modelValue.value = true
        store.add({
            modelValue,
            ...dialogProps,
            componentProps,
            component
        })
    }
    onUnmounted(() => {
        store.remove()
        modelValue.value = false
    })
    return { dialogVisible: modelValue, open }
}
export default DialogStack
