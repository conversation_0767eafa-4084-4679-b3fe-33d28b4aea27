<template>
  <div class="w-full h-8 border-b border-gray-200 flex items-center justify-between select-none drag-region" style="-webkit-app-region: drag" @dblclick="stopResize" @mousedown="handleMouseDown">
    <!-- 左侧标题 -->
    <div class="flex items-center px-4">
      <svg-icon :color="'#fff'" name="Ship" class="mr-2" />
      <span class="text-sm text-white font-medium" v-if="buttons.includes('title')">{{ currentTitle }}</span>
    </div>

    <!-- 右侧窗口控制按钮 -->
    <div class="flex items-center h-full" style="-webkit-app-region: no-drag">
      <el-dropdown trigger="click" class="h-8 w-12 rounded-none flex items-center justify-center transition-colors duration-200" v-if="buttons.includes('avatar')">
        <div class="user-dropdown">
          <el-avatar :size="20">{{ firstName }}</el-avatar>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="goToUserInfo">个人信息</el-dropdown-item>
            <el-dropdown-item divided @click="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <svg-icon
        :color="'#fff'"
        name="sticky"
        size="14"
        :class="{ 'sticky-top': isOnTop }"
        class="h-8 w-12 rounded-none flex items-center justify-center transition-colors duration-200"
        @click="windowAlwaysOnTop"
        v-if="buttons.includes('sticky')" />
      <!-- 最小化按钮 -->
      <el-button
        @click="minimizeWindow"
        link
        class="h-8 w-12 !text-white rounded-none flex items-center justify-center transition-colors duration-200"
        :icon="Minus"
        size="small"
        v-if="buttons.includes('minimize')"></el-button>

      <!-- 最大化/还原按钮 -->
      <!-- <el-button
        @click="toggleMaximize"
        link
        class="h-8 w-12 rounded-none !text-white flex items-center justify-center transition-colors duration-200"
        :icon="isMaximized ? CopyDocument : FullScreen"
        v-if="buttons.includes('maximize')"
        size="small"></el-button> -->

      <!-- 关闭按钮 -->
      <el-button
        @click="closeWindow"
        link
        class="h-8 w-12 rounded-none !text-white hover:text-white flex items-center justify-center transition-colors duration-200"
        :icon="Close"
        v-if="buttons.includes('close')"
        size="small"></el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { Minus, FullScreen, Close, CopyDocument } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import SvgIcon from '@/components/common/SvgIcon.vue'
import useUserStore from '@/store/modules/user'
import { useRoute, useRouter } from 'vue-router'
const userInfo = useUserStore()
const firstName = computed(() => {
  const userName = userInfo.userName || ''
  if (!userName) return ''
  return userName.split(' ')[0].charAt(0)
})
const props = defineProps({
  buttons: {
    type: Array,
    default: () => ['sticky', 'minimize', 'maximize', 'close', 'avatar', 'title']
  }
})
const isMaximized = ref(false)
let currentWindow = null
const isOnTop = ref(false)
const route = useRoute()
const router = useRouter()
const currentTitle = computed(() => {
  return route.meta.title || '船舶与海洋工程结构数据分析及支持服务平台'
})
// 获取当前窗口
const getCurrentWindow = () => {
  if (window.electronAPI) {
    return window.electronAPI
  }
}
const stopResize = e => {
  // 阻止双击最大化行为
  e.preventDefault()
  e.stopPropagation()
  e.stopImmediatePropagation()
  return false
}

// 处理鼠标按下事件，防止意外的拖拽行为
const handleMouseDown = e => {
  // 如果是双击，阻止默认行为
  if (e.detail === 2) {
    e.preventDefault()
    e.stopPropagation()
    return false
  }
}
// 最小化窗口
const minimizeWindow = () => {
  try {
    if (window.electronAPI && window.electronAPI.minimize) {
      window.electronAPI.minimize()
    } else {
      currentWindow.minimize()
    }
  } catch (error) {
    console.error('最小化窗口失败:', error)
  }
}

// 切换最大化状态
const toggleMaximize = () => {
  try {
    if (window.electronAPI && window.electronAPI.toggleMaximize) {
      window.electronAPI.toggleMaximize()
      isMaximized.value = !isMaximized.value
    } else {
      if (currentWindow.isMaximized()) {
        currentWindow.unmaximize()
        isMaximized.value = false
      } else {
        currentWindow.maximize()
        isMaximized.value = true
      }
    }
  } catch (error) {
    console.error('切换最大化状态失败:', error)
  }
}

// 关闭窗口
const closeWindow = () => {
  try {
    if (window.electronAPI && window.electronAPI.close) {
      window.electronAPI.close()
    } else {
      currentWindow.close()
    }
  } catch (error) {
    console.error('关闭窗口失败:', error)
  }
}
// 跳转到用户信息页面
const goToUserInfo = () => {
  window.electronAPI.openNewWindow('/personal-info', { width: 500, height: 400, resizable: false })
  // router.push('/user-management/personal-info')
}

// 退出登录
const logout = async () => {
  try {
    router.push('/login')
    ElMessage.success('退出登录成功')
  } catch (error) {
    router.push('/login')
  }
}
// 更新最大化状态
const updateMaximizedState = () => {
  try {
    if (window.electronAPI && window.electronAPI.isMaximized) {
      window.electronAPI.isMaximized().then(maximized => {
        isMaximized.value = maximized
      })
    } else if (currentWindow) {
      isMaximized.value = currentWindow.isMaximized()
    }
  } catch (error) {
    console.error('获取窗口状态失败:', error)
  }
}

// 监听窗口状态变化
const setupWindowListeners = () => {
  if (window.electronAPI && window.electronAPI.onWindowStateChange) {
    window.electronAPI.onWindowStateChange((event, state) => {
      isMaximized.value = state.isMaximized
    })
  } else if (currentWindow) {
    currentWindow.on('maximize', () => {
      isMaximized.value = true
    })
    currentWindow.on('unmaximize', () => {
      isMaximized.value = false
    })
  }
}
const windowAlwaysOnTop = async () => {
  if (window.electronAPI && window.electronAPI.SwitchTop) {
    window.electronAPI.SwitchTop()
    isOnTop.value = await window.electronAPI.isOnTop()
  }
}
onMounted(() => {
  try {
    if (!window.electronAPI) {
      currentWindow = getCurrentWindow()
    }
    updateMaximizedState()
    setupWindowListeners()
  } catch (error) {
    console.error('初始化窗口控制失败:', error)
  }
})
</script>

<style scoped>
/* 确保按钮样式正确 */
.el-button {
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.el-button:focus {
  outline: none !important;
  box-shadow: none !important;
}
.sticky-top {
  background-color: #e1e1e1;
}

/* 拖拽区域样式 - 防止双击时位置改变 */
.drag-region {
  -webkit-user-select: none;
  user-select: none;
  -webkit-user-drag: none;
  user-drag: none;
  pointer-events: auto;
}

.drag-region * {
  -webkit-user-select: none;
  user-select: none;
  -webkit-user-drag: none;
  user-drag: none;
}
</style>
