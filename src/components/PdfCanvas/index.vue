<template>
  <div ref="pdfRef" class="pdf-view flex items-center justify-center" v-if="cavansRender" @click="handleClick"></div>
  <el-image :src="imgurl" contain :zoom-rate="1.2" :max-scale="7" :min-scale="0.2" v-loading="loading" :preview-src-list="srcList" v-else />
</template>
<script setup>
import { ref, watch } from 'vue'
import PdfjsWorker from 'pdfjs-dist/build/pdf.worker.js?worker'
import { ElImage } from 'element-plus'
const path = window.electronAPI.path
const props = defineProps({
  url: { type: String, required: true },
  maxPage: { type: Number, default: 1 },
  cavansRender: { type: Boolean, default: true }
})
const imgurl = ref('')
const srcList = computed(() => [imgurl.value])
const loading = ref(false)
const pdfRef = ref(null)
// 侦听props的url改变，则立即切换渲染pdf
watch(
  () => props.url,
  () => {
    setTimeout(() => init(), 1)
  },
  { immediate: true }
)
const handleClick = () => {
  window.open(props.url, '_blank', 'width=' + screen.width + ',height=' + screen.height + ',left=0,top=0,scrollbars=yes,resizable=yes')
}
// 渲染pdf
async function init() {
  loading.value = true
  const url = await path.resolve(props.url)
  if (path.extname(url) !== '.pdf') {
    imgurl.value = url
    loading.value = false
    return
  }
  const dom = pdfRef.value
  const finalWidth = window.innerWidth
  const finalHeight = window.innerHeight
  // 异步加载pdf.js
  const PDFJS = await import('pdfjs-dist/build/pdf.js')
  if (typeof window !== 'undefined' && 'Worker' in window) {
    PDFJS.GlobalWorkerOptions.workerPort = new PdfjsWorker()
  }
  // 加载文档
  let loadingTask = PDFJS.getDocument({ url: props.url })
  loadingTask.__PDFDocumentLoadingTask = true
  const pdf = await loadingTask.promise // 使用await等待加载完毕
  window.pdfI = pdf
  // 循环渲染每一页
  for (let i = 1; i <= props.maxPage; i++) {
    const page = await pdf.getPage(i)
    const pixelRatio = 3
    let viewport = page.getViewport({ scale: 1 })
    let divPage = window.document.createElement('div') // canvas的外层div
    // 使用canvas渲染
    let canvas = divPage.appendChild(window.document.createElement('canvas'))
    divPage.className = 'page'
    const ratiox = finalWidth / viewport.width
    const ratioy = finalHeight / viewport.height
    const ratio = Math.min(ratiox, ratioy)
    // canvas.width = viewport.width * ratio // 计算宽度
    // canvas.height = viewport.height * ratio
    canvas.width = viewport.width * pixelRatio // 计算宽度
    canvas.height = viewport.height * pixelRatio
    let renderContext = {
      canvasContext: canvas.getContext('2d'),
      viewport: viewport,
      transform: [pixelRatio, 0, 0, pixelRatio, 0, 0]
    }
    await page.render(renderContext).promise // 一页一页的渲染
    if (props.cavansRender) {
      dom.appendChild(divPage)
    } else {
      imgurl.value = canvas.toDataURL()
    }
    divPage.className = 'page complete'
    loading.value = false
  }
}
</script>
<style scoped lang="scss">
.pdf-view {
  position: relative;
  width: 100%;
  height: 100%;
}
// 组件样式
:deep() {
  .page {
    position: relative;
    canvas {
      width: 100%;
    }
  }
}
</style>
