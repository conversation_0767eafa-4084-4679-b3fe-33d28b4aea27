.mtable__select--single {
    thead th .el-checkbox {
        display: none;
    }
}

.mtable__page {
    margin-top: 20px;
    justify-content: flex-end;
    margin-top: 10px;

    .el-pagination__rightwrapper {
        flex: none;
    }
}

/* 设置滚动条宽度和高度 */
.el-table__body-wrapper::-webkit-scrollbar {
    width: 20px;
    /* 横向滚动条 */
    height: 8px;
    /* 纵向滚动条 必写 */
}

/* 设置滚动条样式 */
.el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: #dde;
    border-radius: 3px;
}