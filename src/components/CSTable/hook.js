import { reactive, ref } from "vue"
export function useTable(config) {
  const page = reactive({ pageNo: 1, pageSize: 10, total: 0 })
  const data = config?.data ?? ref([])
  const columns = config.columns ?? []
  const getTable = async () => {
    if (!config.api) return
    const query = unref(config?.query ?? {})
    const res = await config.api({
      ...page,
      ...query
    })
    if (res && res.code === 200) {
      data.value = res?.data?.rows ?? []
      page.total = res?.data?.total ?? 0
      return res.data
    }
    return
  }
  const search = async () => {
    page.pageNo = 1
    return await getTable()
  }
  config.immediate && getTable()
  function updatePage(pageNo, pageSize) {
    page.pageNo = pageNo
    page.pageSize = pageSize
    getTable()
  }
  return { data, page, columns, search, updatePage, getTable }
}

