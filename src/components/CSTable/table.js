import { defineComponent, reactive, render, watch, watchEffect } from "vue"
import { ElTableColumn, ElTable, ElPagination, ElInput } from "element-plus"
import { isFunction } from "lodash-es"
import CSSelect from "@/components/CSSelect/index.vue"
import './mtable.scss'

const Table = defineComponent({
  props: {
    data: {
      type: [Array, Object],
      default() {
        return []
      },
    },
    columns: {
      type: Array,
      default() {
        return []
      },
    },
    pagination: {
      type: [Object, Boolean],
      default: null,
    },
    op: {
      type: [Object, Boolean],
      default: null,
    },
    seq: {
      type: [Boolean, Object],
      default: false
    },
    select: {
      type: [String, Boolean],
      default: false
    },
  },
  emits: ["pageChange"],
  setup(props, { expose, emit }) {
    const slots = useSlots()
    const innerPage = reactive(toRaw(props.pagination))
    watchEffect(() => {
      innerPage.pageNo = props.pagination.pageNo
      innerPage.pageSize = props.pagination.pageSize
      innerPage.total = props.pagination.total
    })
    const pageChange = (pageNo, pageSize) => {
      innerPage.pageNo = pageNo
      innerPage.pageSize = pageSize
    }
    watch([() => innerPage.pageNo, () => innerPage.pageSize], ([on, os], [nn, ns]) => {
      if (on !== nn || os !== ns) {
        emit("pageChange", innerPage.pageNo, innerPage.pageSize)
      }
    })
    const selectedRow = ref([])
    // 选择逻辑
    const onSelectionChange = (val) => {
      selectedRow.value = val
    }
    const clearSelection = () => {
      tableRef.value.clearSelection()
      selectedRow.value = []
    }
    const selectable = (row) => {
      if (props.select === "single") {
        if (selectedRow.value.length) {
          return row === selectedRow.value[0]
        } else {
          return true
        }
      }
      return true
    }
    const renderColumns = (columns) => {
      const col = columns.map((col) => {
        return renderSingleCol(col)
      })
      if (slots.op && props.op) {
        const op = typeof props.op === "boolean" ? {} : props.op
        col.push(renderSingleCol({ render: slots.op, ...op, title: "操作" }))
      }
      if (props.seq) {
        if (typeof props.seq === "boolean") {
          col.unshift(renderSingleCol({ type: "index", title: "序号" }))
        }
        if (typeof props.seq === "object" && props.seq !== null) {
          col.unshift(renderSingleCol({ ...props.seq, type: "index", title: "序号" }))
        }
      }
      if (props.select) {
        col.unshift(h(ElTableColumn, { type: "selection", selectable }))
      }
      return col
    }
    expose({
      selectedRow,
      clearSelection,
    })
    return () => {
      const children = [
        h(
          ElTable,
          {
            data: unref(props.data),
            class: { "mtable__select--single": !!(props.select === "single") },
            onSelectionChange
          },
          () => renderColumns(props.columns),
        ),
      ]
      if (props.pagination) {
        children.push(h(ElPagination, {
          onChange: pageChange,
          total: innerPage.total,
          class: "mt-3 mtable__page",
          layout: 'sizes,prev, pager, next, jumper, ->, total',
          pageSizes: [5, 10, 30,],
          "defaultPageSize": props.pagination.pageSize,
          "currentPage": innerPage.pageNo,
          "onUpdate:currentPage": (v) => {
            innerPage.pageNo = v
          },
        }))
      }
      return h("div", {}, children)
    }
  },
})
function renderSingleCol(col) {
  // 序号列
  const renderSeq = (scope) => {
    return h("div", { class: "text-center" }, scope.$index + 1)
  }
  // 下拉框
  const renderSelect = (scope, edit, col) => {
    if (edit) {
      return h(CSSelect, {
        list: col.list,
        modelValue: scope.row[col.field],
        'onUpdate:modelValue': (v) => {
          console.log(v, '121')
          scope.row[col.field] = v
        }
      })
    }
    return h("div", col.list.find(item => item.value === scope.row[col.field])?.label)
  }
  // 自定义列
  const renderSolts = (col, scope) => {
    const value = scope.row[col.field]
    if (isFunction(col.render)) {
      return col.render({
        value,
        row: scope.row,
        index: scope.$index,
      })
    } else if (col.render) {
      return slots[col.render]({ value, row: scope.row, index: scope.$index })
    }
  }
  // 默认列
  const renderDefault = (col, scope) => {
    if (col.edit) {
      return h(ElInput, {
        modelValue: scope.row[col.field],
        'onUpdate:modelValue': (v) => {
          scope.row[col.field] = v
        },
      })
    }
    if (col.formatter) {
      return h("div", col.formatter(scope.row[col.field], scope))
    }
    return h("div", scope.row[col.field])
  }
  return h(
    ElTableColumn,
    {
      ...col,
      prop: col?.field ?? null,
      label: isRef(col?.title) ? col?.title?.value : col?.title,
    },
    {
      default(scope) {
        if (col.type === 'index') return renderSeq(scope)
        if (col.render) return renderSolts(col, scope)
        if (col.list) return renderSelect(scope, col.edit, col)
        if (col.type === 'selection') return renderSelection(scope)
        return renderDefault(col, scope)
      },
      header() {
        if (col.header) {
          return col.header()
        }
        return h("div", {}, col.title)
      }
    },

  )
}
// const renderHeader = (col) => {
export default Table
