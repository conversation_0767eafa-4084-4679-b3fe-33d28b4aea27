<template>
  <div class="captcha-container cursor-pointer" :style="{ width: width + 'px', height: height + 'px' }" @click="refreshCaptcha">
    <img v-if="captchaUrl" :src="captchaUrl" class="w-full h-full object-fill" alt="验证码" />
    <div v-else class="w-full h-full flex items-center justify-center bg-gray-100">
      <el-icon class="animate-spin text-gray-400"><Loading /></el-icon>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
const props = defineProps({
  width: {
    type: Number,
    default: 120
  },
  height: {
    type: Number,
    default: 40
  },
  reqId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:reqId'])

const captchaUrl = ref('')
</script>

<style scoped>
.captcha-container {
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
}

.captcha-container:hover {
  border-color: #409eff;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.2);
}
</style>
