<template>
  <div v-if="name" class="svg-icon">
    <svg aria-hidden="true" :style="{ fill: color, width: size, height: size }">
      <use :xlink:href="iconName"></use>
    </svg>
  </div>
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
  // 图标名称
  name: {
    type: String,
    default: "",
  },
  // 图标颜色
  color: {
    type: String,
    default: "currentColor",
  },
  // 图标大小
  size: {
    type: String,
    default: "16px",
  },
});

// 计算完整的图标名称
const iconName = computed(() => {
  return props.name ? `#icon-${props.name}` : "";
});
</script>

<style scoped>
.svg-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
</style>
