import { ElButton, ElCol, ElForm, ElFormItem, ElRow, ElTooltip, useProp } from "element-plus"
import componentMap from "@/components/renderer/componentMap.js"
import { unref, defineComponent, ref, h, useAttrs, useSlots, computed } from "vue"
import { useForm } from './useForm'
import { useMedia } from './useMedia.js'
import { useReplaceTooltip } from '@/use/useReplaceTooltip'
import { ArrowUp, ArrowDown } from "@element-plus/icons-vue"
const labelComponent = defineComponent({
  props: {
    label: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const { label } = props
    if (typeof label === "string") {
      const { tooltipsDisabled, labelEl } = useReplaceTooltip({ text: label })
      return () => h(ElTooltip, {
        content: label,
        effect: 'dark',
        disabled: tooltipsDisabled.value,
        placement: 'top'
      }, {
        default: () => h("div", {
          ref: labelEl,
          class: "truncate overflow-hidden whitespace-nowrap",
        }, label)
      })
    }
    return () => h('div', {}, () => item?.label())
  }
})
const CsForm = defineComponent({
  props: {
    options: {
      type: Array,
      default: () => [],
    },
    layout: {
      type: Object,
      default: () => { },
    },
    model: {
      type: Object,
      default: () => { },
    },
    labelWidth: {
      type: String,
      default: null,
    },
    handler: {
      type: Object,
      default: null,
    },
    rules: {
      type: Object,
      default: null
    },
    search: {
      type: Function,
      default: null
    },
    reset: {
      type: Function,
      default: null
    },
    fold: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { expose, emit }) {
    const attrs = useAttrs()
    const slots = useSlots()
    // 折叠，响应式显示第一行的个数，需要查询了解与element默认布局一致
    const xs = useMedia('(max-width: 767px)', (e) => {
      if (props.layout?.xs && e.matches) {
        flodCount.value = Math.floor(24 / props.layout?.xs)
      }
    })
    const sm = useMedia('(min-width: 768px) and (max-width: 991px)', (e) => {
      if (props.layout?.sm && e.matches) {
        flodCount.value = Math.floor(24 / props.layout?.sm)
      }
    })
    const md = useMedia('(min-width: 992px) and (max-width: 1199px)', (e) => {
      if (props.layout?.md && e.matches) {
        flodCount.value = Math.floor(24 / props.layout?.md)
      }
    })
    const lg = useMedia('(min-width: 1200px) and (max-width: 1919px)', (e) => {
      if (props.layout?.lg && e.matches) {
        flodCount.value = Math.floor(24 / props.layout?.lg)
      }
    })
    const xl = useMedia('(min-width: 1920px)', (e) => {
      if (props.layout?.xl && e.matches) {
        flodCount.value = Math.floor(24 / props.layout?.xl)
      }
    })
    const str = (() => {
      if (xs) return 'xs';
      if (sm) return 'sm';
      if (md) return 'md';
      if (lg) return 'lg';
      if (xl) return 'xl';
    })()
    const isFolded = ref(true)
    const flodCount = ref(Math.floor(24 / props.layout?.[str]))
    const { form, options, formRef, rules, reset, submit, validate, resetFields } = useForm(props.model, unref(props.options), props.rules, { onReset: () => props?.reset?.(form) ?? null, onSearch: () => props?.search?.(form) ?? null })
    const renderBtns = (handler) => {
      return h(ElCol, { ...props?.layout }, () => h(ElFormItem, null, () => [h(ElButton, { type: "primary", onclick: submit }, () => handler.sBtn), h(ElButton, { type: "primary", onclick: reset }, () => handler.cBtn)]))
    }
    const renderFormItems = (options, slots) => {
      const displayLength = computed(() => {
        if (props.fold && isFolded.value) {
          return flodCount.value
        }
        return options.length
      })
      const inputs = options.slice(0, displayLength.value).map((item, index) => {
        const layout = item?.layout ?? props.layout ?? {}
        return h(
          ElCol,
          { ...layout, style: { display: ((index >= flodCount.value) && isFolded.value && props.fold) ? 'none' : 'block' } },
          () => h(
            ElFormItem,
            { labelWidth: props?.labelWidth ?? null, ...(item?.fOps ?? {}), prop: item.field },
            {
              default: () => rendComponent(item, slots),
              label: () => h(labelComponent, { label: item.formLabel, labelWidth: props?.labelWidth ?? null }),
            },
          ),
        )
      })
      if (props.handler) {
        const handler = props.handler
        inputs.push(renderBtns(handler))
      }
      return inputs
    }
    const rendComponent = (item, slots) => {
      if (slots[item.field]) return slots[item.field]()
      if (item.render) return item.render()
      if (item.type === "Rinput") {
        return h(componentMap[item.type], {
          model: form,
          ...item,
          disabled: props.disabled,
          min: form[item.field[0]],
          max: form[item.field[1]],
          "onUpdate:min": (val) => {
            form[item.field[0]] = val
          },
          "onUpdate:max": (val) => {
            form[item.field[1]] = val
          },
        })
      }
      if (!item.type) {
        return h('div', '')
      }
      return h(componentMap[item.type], {
        model: form,
        ...item,
        type: item.ctype ?? null,
        disabled: props.disabled,
        modelValue: form[item.field],
        "onUpdate:modelValue": (val) => {
          form[item.field] = val
        },
      })
    }
    // 折叠展开按钮
    const renderFoldBtn = () => {
      if (!props.fold) return h('div', { class: 'w-[44px] inline-flex' })
      return h(ElButton,
        { type: "primary", icon: isFolded.value ? ArrowDown : ArrowUp, style: { height: 'fit-content' }, class: 'w-[44px]', link: true, onclick: () => isFolded.value = !isFolded.value },
        () => isFolded.value ? "展开" : "收起"
      )
    }
    const getValue = () => {
      return form
    }
    expose({
      getValue,
      submit,
      validate,
      reset,
      resetFields
    })
    return () => h(
      'div',
      { ...attrs, class: 'flex' },
      [h(ElForm, { ref: formRef, labelPosition: attrs.labelPosition ?? "left", model: form, rules, class: ['mr-5', 'flex-1'] }, () => h(ElRow, { gutter: attrs.gutter ?? 20 }, () => renderFormItems(options, slots))), renderFoldBtn()]
    )
  },
})
export default CsForm
