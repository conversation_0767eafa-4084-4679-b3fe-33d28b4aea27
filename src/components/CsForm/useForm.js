import { cloneDeep, merge } from 'lodash-es'
export const useForm = (f, op = [], rules, callbacks = {}) => {
    const formRef = ref()
    const orF = cloneDeep(f)
    const form = reactive(f);
    const options = op || [];
    const resetFrom = () => {
        Object.keys(form).forEach((key) => {
            form[key] = "";
        });
    };
    const resetFields = () => merge(form, cloneDeep(orF))
    const reset = () => {
        resetFields()
        callbacks.onReset?.()
    }
    const submit = async () => {
        if (!rules) {
            return await callbacks.onSearch?.()
        }
        const valid = await formRef.value.validate()
        if (valid) {
            return await callbacks.onSearch?.()
        } else {
            return await Promise.reject(112)
        }
    }
    const validate = async () => {
        const r = await formRef.value.validate()
        return r
    }
    return { form, resetFrom, options, formRef, rules, reset, submit, validate, resetFields };
};