import { <PERSON><PERSON>utton, ElIn<PERSON>, ElCheckbox } from "element-plus"
import CSSelect from "@/components/CSSelect/index.vue"
import RangeInput from "@/components/RangeInput/RangeInput.vue"
import CSTable from "@/components/CSTable/index.js"

const componentMap = {}
const registryComponent = (component, options) => {
  componentMap[options.name] = component
}
registryComponent(ElInput, { name: "input" })
registryComponent(CSSelect, { name: "select" })
registryComponent(RangeInput, { name: "Rinput" })
registryComponent(ElButton, { name: "btn" })
registryComponent(ElButton, { name: "btn" })
registryComponent(CSTable, { name: "table" })
registryComponent(ElCheckbox, { name: "checkbox" })
export default componentMap
