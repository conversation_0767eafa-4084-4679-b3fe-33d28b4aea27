import { <PERSON><PERSON>ol, <PERSON><PERSON>ow, <PERSON><PERSON>ool<PERSON>, ElCollapseTransition } from "element-plus"
import { defineComponent, useAttrs, useSlots, h, ref, onMounted } from 'vue'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import './index.scss'

// 判断文字是否超出容器宽度
const isTextOverflow = (element) => {
    if (!element) return false
    return element.scrollWidth > element.clientWidth
}

export const CSDescriptionItem = defineComponent({
    props: {
        label: {
            type: [String, Object],
            default: ''
        },
        value: {
            type: [String, Number, Object],
            default: ''
        },
        horizontal: {
            type: Boolean,
            default: true
        }
    },
    setup(props) {
        const attrs = useAttrs()
        const slots = useSlots()
        const labelRef = ref(null)
        const isOverflow = ref(false)

        onMounted(() => {
            if (labelRef.value) {
                isOverflow.value = isTextOverflow(labelRef.value)
            }
        })

        const renderLabel = () => {
            const labelContent = h('div', {
                ref: labelRef,
                class: 'min-w-[90px] shrink-0 text-left px-[8px] py-[11px] text-xs text-[#606266] border-r-[1px] border-r-[#ebeef5] border-r-solid truncate'
            }, {
                default: () => slots.label?.() || props.label
            })

            return isOverflow.value
                ? h(ElTooltip, {
                    content: slots.label?.() || props.label,
                    placement: 'top',
                    effect: 'dark'
                }, {
                    default: () => labelContent
                })
                : labelContent
        }

        return () => h(ElCol, { span: attrs.span, ...attrs.layout }, renderContent(renderLabel(), props.value, slots, props.horizontal, attrs.schema))
    }
})

function renderContent(label, value, slots, horizontal, schema) {
    return () => h('div', { class: `flex items-stretch bg-[#f5f7fa] outline-1 outline-[#ebeef5] outline flex-1 ${horizontal ? 'flex-row' : 'flex-col'}` }, [
        label,
        h('div', { class: "flex-1 px-[8px] py-[11px] bg-[#ffffff] text-xs text-[#303133] text-size-[14px]" }, {
            default: () => {
                if (slots.default) {
                    return slots.default()
                } else if (schema.formater) {
                    return schema.formater(value) || '-'
                }
                else if (value === '' || value === null || value === undefined) {
                    return '-'
                } else {
                    return value
                }
            }
        })
    ])
}
export default defineComponent({
    props: {
        schema: {
            type: Array,
            default: []
        },
        data: {
            type: Object,
            default: null
        },
        columns: {
            type: Number,
            default: 2
        },
        layout: {
            type: Object,
            default: () => ({ xl: 24, lg: 24, md: 12, sm: 6, xs: 4 })
        },
        horizontal: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: null
        }
    },
    setup(props) {
        const span = Math.floor(24 / props.columns)
        const show = ref(true)
        const contents = () => props.schema.map(x => {
            const value = props.data[x.key]
            const costumSpan = x.span
            const costumLayout = x.layout || props.layout
            const slot = x.slot
            return h(CSDescriptionItem, { label: x.label, value: value, span: costumSpan || span, layout: costumLayout, horizontal: props.horizontal, schema: x }, slot)
        })
        const renderTitle = () => {
            return h('div', {
                class: 'w-full flex items-center justify-between cursor-pointer mb-3 transition-colors duration-200 hover:bg-gray-50 rounded-lg p-2',
                onClick: () => show.value = !show.value
            }, [
                h('div', { class: 'text-md font-medium text-gray-700' }, props.title),
                show.value ? h(ArrowUp, { class: 'text-xs w-3 h-3' }) : h(ArrowDown, { class: 'text-xs w-3 h-3' })
            ])
        }
        return () => h('div', {
            class: 'cs-description-contaner'
        }, [props.title ? renderTitle() : null, h(ElCollapseTransition, {}, () => show.value ? h(ElRow, {}, contents) : null)])
    }
})

