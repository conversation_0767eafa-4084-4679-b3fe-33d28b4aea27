<template>
  <Frame class="fixed z-3 bg-sky-500" v-if="!isNoFrame" />
  <router-view class="pt-8" />
</template>

<script setup>
import Frame from './components/frame/index.vue'
import { computed } from 'vue'
import { useRoute } from 'vue-router'
const route = useRoute()
const noFramePages = ['/login', '/personal-info']
const isNoFrame = computed(() => noFramePages.includes(route.path))
</script>

<style lang="scss" scoped>
html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}
/* 表头 */
:deep(.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th) {
  background: #deeeff !important;
}

/* 斑马纹表格样式 */
/* // 奇数行 */
:deep(.el-table__body-wrapper tbody tr:nth-child(odd)) {
  background-color: #fff !important;
}
/* // 偶数行 */
.el-table__body-wrapper tbody tr:nth-child(even) {
  background-color: #f5f8fb !important;
}
</style>
