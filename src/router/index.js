import { createRouter, createWebHashHistory } from 'vue-router'
import MainLayout from '@/layouts/MainLayout.vue'
import redircet from "@/views/redirect/index.vue"
import home from "@/views/home/<USER>"
import shipList from "@/views/ship/list/index.vue"
import shipDetail from "@/views/ship/detail/index.vue"
import dataApplication from "@/views/data/application/index.vue"
import dataExport from "@/views/data/export/index.vue"
import dataImport from "@/views/data/import/index.vue"
import dataEntry from "@/views/data/entry/index.vue"
import personalInfo from "@/views/user/personal-info/index.vue"

import { setupPermission } from './permission'
const router = createRouter({
    history: createWebHashHistory(),
    routes: [
        {
            path: '/',
            component: MainLayout,
            redirect: '/home',
            name: 'main',
            children: [
                // 主页
                {
                    name: "home",
                    path: "home",
                    component: home,
                    meta: {
                        title: "主页",
                        icon: "HomeFilled"
                    },
                },
                // 船舶列表
                {
                    name: "shipList",
                    path: "ship-list",
                    component: shipList,
                    meta: {
                        title: "船舶列表",
                        icon: "Ship"
                    }
                },
                // 船舶详情
                {
                    name: "shipDetail",
                    path: "shipList-detail/:id",
                    component: shipDetail,
                    meta: {
                        title: "船舶详情",
                        icon: "InfoFilled"
                    },
                    hidden: true,
                    children: []
                },
                // 数据申请
                {
                    name: "dataApplication",
                    path: "data-application",
                    component: dataApplication,
                    meta: {
                        title: "数据申请",
                        icon: "DocumentAdd"
                    }
                },
                // 数据导出
                {
                    name: "dataExport",
                    path: "data-export",
                    component: dataExport,
                    meta: {
                        title: "导出记录",
                        icon: "Download"
                    }
                },
                // 数据导入
                {
                    name: "dataImport",
                    path: "data-import",
                    component: dataImport,
                    meta: {
                        title: "数据导入",
                        icon: "Upload"
                    }
                },
                // 数据录入
                {
                    name: "dataEntry",
                    path: "data-entry",
                    component: dataEntry,
                    meta: {
                        title: "数据录入",
                        icon: "EditPen"
                    }
                },

                // 个人信息页面
                // {
                //     name: "personalInfo",
                //     path: "user-management/personal-info",
                //     component: personalInfo,
                //     meta: {
                //         title: "个人信息",
                //         icon: "UserFilled"
                //     },
                //     hidden: true
                // },

                {
                    path: "redirect/:path(.*)",
                    component: redircet,
                    hidden: true,
                },
            ]
        },
        // 个人信息页面
        {
            name: "personalInfo",
            path: "/personal-info",
            component: personalInfo,
            meta: {
                title: "个人信息",
                icon: "UserFilled"
            },
            hidden: true
        },
        // 登录页面
        {
            path: '/login',
            component: () => import('@/views/login/index.vue'),
            hidden: true
        },
        // 错误页面
        {
            path: '/401',
            component: () => import('../views/error/401.vue'),
            hidden: true
        },
        {
            path: '/404',
            component: () => import('../views/error/404.vue'),
            hidden: true
        },
        {
            path: '/:pathMatch(.*)*',
            redirect: '/404'
        }
    ]
})

// 设置路由守卫
setupPermission(router)

export default router 