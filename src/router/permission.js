import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { ElMessage } from 'element-plus'
import { useUserStore, usePermissionStore } from '@/store'

// 配置NProgress
NProgress.configure({ showSpinner: false })

// 白名单路由（不需要登录就可以访问）
const whiteList = ['/login', '/404', '/401']
const beforeTologin = (to, from) => {
    const toPath = to.path
    const fromPath = from.path
    if (toPath === '/login') {
        window.electronAPI.fixedSize({ width: 500, height: 400 })
    }
    if (fromPath === '/login') {
        window.electronAPI.normalSize()
    }
}
export function setupPermission(router) {
    // 路由守卫
    router.beforeEach(async (to, from, next) => {
        // 开始进度条
        NProgress.start()
        // 设置页面标题
        document.title = to.meta.title ? `${to.meta.title} - ${import.meta.env.VITE_SYSTEM_NAME}` : import.meta.env.VITE_SYSTEM_NAME

        // 使用Store获取token
        const userStore = useUserStore()
        const permissionStore = usePermissionStore()
        const token = userStore.token
        if (token) {
            // if (true) {
            // 已登录
            if (to.path === '/login') {
                // 如果已登录，访问登录页则重定向到首页
                beforeTologin(to, from)
                next()
                NProgress.done()
            } else {
                // 判断是否已获取用户信息
                if (!userStore.userId) {
                    // if (false) {
                    try {
                        // 获取用户信息
                        await userStore.getInfo()

                        // 生成 可访问路由
                        await permissionStore.generateRoutes()
                        beforeTologin(to, from)
                        // 正常访问
                        next({ ...to, replace: true })
                    } catch (error) {
                        console.log(error)
                        // 获取用户信息失败，可能是token过期
                        await userStore.resetToken()
                        ElMessage.error('登录状态已过期，请重新登录')
                        beforeTologin(to, from)
                        next(`/login?redirect=${to.path}`)
                        NProgress.done()
                    }
                } else {
                    // 正常访问其他页面
                    await permissionStore.generateRoutes()
                    beforeTologin(to, from)
                    next()
                }
            }
        } else {
            // 未登录
            if (whiteList.includes(to.path)) {
                beforeTologin(to, from)
                next();
                // 在白名单中，直接进入
            } else {
                beforeTologin(to, from)
                // 不在白名单中，重定向到登录页，并携带原目标路径
                next(`/login?redirect=${to.path}`)
                NProgress.done()
            }
        }
    })
    router.afterEach((to, from, failure) => {
        // 结束进度条
        NProgress.done()
    })
} 