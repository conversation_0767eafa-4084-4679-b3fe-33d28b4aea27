export default {
    path: '/lightweightComponent',
    component: () => import('@/layouts/MainLayout.vue'),
    children: [
        {
            path: 'layerPlate',
            name: 'layerPlate',
            component: () => import("@/views/lightweightComponent/layerPlate/index.vue"),
            meta: {
                menuName: 'layerPlate',
                title: '层合板单元'
            }
        },
        {
            path: 'layerPlate/detail/:id',
            name: 'layerPlate-detail',
            component: () => import("@/views/lightweightComponent/layerPlate/LayerPlateDetail.vue"),
            props: true,
            meta: {
                menuName: 'layerPlate',
                title: '层合板单元'
            }
        },
        {
            path: 'hatBeam',
            name: 'hatBeam',
            component: () => import("@/views/lightweightComponent/hatBeam/index.vue"),
            meta: {
                menuName: 'hatBeam',
                title: '帽型梁单元'
            }
        },
        {
            path: 'hatBeam/detail/:id',
            name: 'hatBeam-detail',
            component: () => import("@/views/lightweightComponent/hatBeam/HatBeamDetail.vue"),
            props: true,
            meta: {
                menuName: 'hatBeam',
                title: '帽型梁单元'
            }
        },
        {
            path: 'flatBeam',
            name: 'flatBeam',
            component: () => import("@/views/lightweightComponent/flatBeam/index.vue"),
            meta: {
                menuName: 'flatBeam',
                title: '扁型梁单元'
            }
        },
        {
            path: 'flatBeam/detail/:id',
            name: 'flatBeam-detail',
            component: () => import("@/views/lightweightComponent/flatBeam/FlatBeamDetail.vue"),
            props: true,
            meta: {
                menuName: 'flatBeam',
                title: '扁型梁单元'
            }
        },
        {
            path: 'hBeam',
            name: 'hBeam',
            component: () => import("@/views/lightweightComponent/hBeam/index.vue"),
            meta: {
                menuName: 'hBeam',
                title: 'H型梁单元'
            }
        },
        {
            path: 'hBeam/detail/:id',
            name: 'hBeam-detail',
            component: () => import("@/views/lightweightComponent/hBeam/HBeamDetail.vue"),
            props: true,
            meta: {
                menuName: 'hBeam',
                title: 'H型梁单元'
            }
        },
        {
            path: 'flange',
            name: 'flange',
            component: () => import("@/views/lightweightComponent/flange/index.vue"),
            meta: {
                menuName: 'flange',
                title: '法兰单元'
            }
        },
        {
            path: 'flange/detail/:id',
            name: 'flange-detail',
            component: () => import("@/views/lightweightComponent/flange/FlangeDetail.vue"),
            props: true,
            meta: {
                menuName: 'flange',
                title: '法兰单元'
            }
        },
        {
            path: 'lBeam',
            name: 'lBeam',
            component: () => import("@/views/lightweightComponent/lBeam/index.vue"),
            meta: {
                menuName: 'lBeam',
                title: 'L型梁单元'
            }
        },
        {
            path: 'lBeam/detail/:id',
            name: 'lBeam-detail',
            component: () => import("@/views/lightweightComponent/lBeam/LBeamDetail.vue"),
            props: true,
            meta: {
                menuName: 'lBeam',
                title: 'L型梁单元'
            }
        },
        {
            path: 'pillar',
            name: 'pillar',
            component: () => import("@/views/lightweightComponent/pillar/index.vue"),
            meta: {
                menuName: 'pillar',
                title: '支柱单元'
            }
        },
        {
            path: 'pillar/detail/:id',
            name: 'pillar-detail',
            component: () => import("@/views/lightweightComponent/pillar/PillarDetail.vue"),
            props: true,
            meta: {
                menuName: 'pillar',
                title: '支柱单元'
            }
        },
        {
            path: 'sandwichPlate',
            name: 'sandwichPlate',
            component: () => import("@/views/lightweightComponent/sandwichPlate/index.vue"),
            meta: {
                menuName: 'sandwichPlate',
                title: '夹层板单元'
            }
        },
        {
            path: 'sandwichPlate/detail/:id',
            name: 'sandwichPlate-detail',
            component: () => import("@/views/lightweightComponent/sandwichPlate/SandwichPlateDetail.vue"),
            props: true,
            meta: {
                menuName: 'sandwichPlate',
                title: '夹层板单元'
            }
        },
        {
            path: 'tBeam',
            name: 'tBeam',
            component: () => import("@/views/lightweightComponent/tBeam/index.vue"),
            meta: {
                menuName: 'tBeam',
                title: 'T型梁单元'
            }
        },
        {
            path: 'tBeam/detail/:id',
            name: 'tBeam-detail',
            component: () => import("@/views/lightweightComponent/tBeam/TBeamDetail.vue"),
            props: true,
            meta: {
                menuName: 'tBeam',
                title: 'T型梁单元'
            }
        },
        {
            path: 'driveShaft',
            name: 'driveShaft',
            component: () => import("@/views/lightweightComponent/driveShaft/index.vue"),
            meta: {
                menuName: 'driveShaft',
                title: '传动轴单元'
            }
        },
        {
            path: 'driveShaft/detail/:id',
            name: 'driveShaft-detail',
            component: () => import("@/views/lightweightComponent/driveShaft/DriveShaftDetail.vue"),
            props: true,
            meta: {
                menuName: 'driveShaft',
                title: '传动轴单元'
            }
        }
    ]
} 