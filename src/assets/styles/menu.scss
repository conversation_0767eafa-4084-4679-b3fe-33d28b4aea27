/* 顶部菜单样式 */
.el-menu--horizontal {
  background-color: transparent !important;
}

/* 顶部菜单文字颜色 */
.el-menu--horizontal>.el-menu-item {
  color: white !important;
}

.el-menu--horizontal>.el-menu-item.is-active {
  color: white !important;
  font-weight: bold !important;
  border-bottom-color: #ffd04b !important;
  background-color: transparent !important;
}

.el-menu--horizontal>.el-menu-item:hover {
  color: #ffffff !important;
  background-color: rgba(255, 255, 255, 0.15) !important;
}

/* 顶部栏所有文字 */
.app-header * {
  color: white !important;
}

/* 顶部栏下拉菜单文字 */
.el-dropdown-menu__item {
  color: #333 !important;
}

.el-dropdown-menu__item:hover {
  color: #409eff !important;
}

/* 侧边菜单样式 */
.left-menu {
  background-color: transparent !important;
}

/* 侧边栏菜单项 */
.left-menu .el-menu-item,
.left-menu .el-sub-menu__title {
  color: rgba(255, 255, 255, 0.8) !important;
  transition: all 0.3s !important;
}

.left-menu .el-menu-item:hover,
.left-menu .el-sub-menu__title:hover {
  color: white !important;
}

.left-menu .el-menu-item.is-active {
  color: #ffd04b !important;
}



/* 三级菜单样式 */
.el-menu--popup {
  background-color: #1e3799 !important;
  border: none !important;
}

.el-menu--popup .el-menu-item {
  color: rgba(255, 255, 255, 0.8) !important;
  background-color: #1e3799 !important;
}

.el-menu--popup .el-menu-item:hover {
  background-color: #2c4494 !important;
}

.el-menu--popup .el-menu-item.is-active {
  color: #ffd04b !important;
}

/* 子菜单弹出层样式 */
.el-menu--vertical .el-menu-item.is-active {
  color: #ffd04b !important;
}

/* 处理嵌套层级的缩进 */
.el-menu .el-menu .el-menu .el-menu-item {
  padding-left: 74px !important;
}

/* 侧边栏折叠状态 */
.el-menu--collapse {
  width: 64px;
}

.el-menu--collapse .el-sub-menu__title span,
.el-menu--collapse .el-menu-item span {
  display: none;
}

.el-menu--collapse .el-sub-menu__icon-arrow {
  display: none;
}


/* 折叠状态下的菜单项样式 */
.el-menu--collapse .el-menu-item [class^="el-icon-"],
.el-menu--collapse .el-menu-item i,
.el-menu--collapse .el-submenu__title [class^="el-icon-"],
.el-menu--collapse .el-submenu__title i {
  margin: 0;
  vertical-align: middle;
  width: 24px;
  text-align: center;
}

.el-menu--collapse .el-menu-item,
.el-menu--collapse .el-submenu__title {
  text-align: center;
}

// /* 滚动条样式 */
// .el-scrollbar__bar.is-vertical {
//   width: 6px !important;
// }

// .el-scrollbar__thumb {
//   background-color: rgba(255, 255, 255, 0.3) !important;
//   border-radius: 3px !important;
// }

// .el-scrollbar__thumb:hover {
//   background-color: rgba(255, 255, 255, 0.5) !important;
// }

// .app-aside .el-scrollbar__wrap {
//   overflow-x: hidden !important;
// }