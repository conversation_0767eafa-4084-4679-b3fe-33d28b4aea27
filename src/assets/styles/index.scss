@use "./element/index.scss" as *;

/* 引入所需组件样式 */
@use "element-plus/theme-chalk/src/index.scss";
// @import "./mixin.scss";
// @import "./transition.scss";
// @import "./element-ui.scss";
// @import "./btn.scss";
@import "./base.scss";
// @import "./global.scss";
@import "./menu.scss";

@tailwind base;
@tailwind components;
@tailwind utilities;

.label-w-50 {
  @apply w-[50px];
}

.label-w-100 {
  @apply w-[100px];
}

.label-w-150 {
  @apply w-[150px];
}

/* 添加响应式变体 */
.sm-label-w-50 {
  @apply sm:w-[50px];
}

.sm-label-w-100 {
  @apply sm:w-[100px];
}

body {
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
  // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  // padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  //   margin-top: 30px;
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

/* 自定义滚动条样式 */
/* webkit 内核浏览器 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  /* 纵向滚动条 宽度 */
  height: 8px;
  /* 横向滚动条 高度 */
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background 0.3s ease;
}

/* 滚动条滑块 hover 状态 */
::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 滚动条拐角 */
::-webkit-scrollbar-corner {
  background: #f1f1f1;
}

/* 针对不同状态的滚动条样式 */
/* 深色主题滚动条 */
.dark-theme {
  ::-webkit-scrollbar-track {
    background: #2c2c2c;
  }

  ::-webkit-scrollbar-thumb {
    background: #555;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #777;
  }
}

/* 纤细滚动条样式 */
.thin-scrollbar {
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5);
  }
}

/* Element Plus 组件内的滚动条样式 */
.el-scrollbar__wrap {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 表格滚动条样式 */
.el-table .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.el-table .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 3px;
}

.el-table .el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background: #bbb;
}

.el-divider--horizontal {
  margin: 16px 0 !important;
}

.el-textarea.el-input--small {
  .el-textarea__inner {
    min-height: 24px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    line-height: 24px;
  }
}