import { hasPermission } from '@/utils/permission'

export default {
    mounted(el, binding) {
        const { value } = binding
        const roles = JSON.parse(localStorage.getItem('roles') || '[]')

        if (value && value instanceof Array && value.length > 0) {
            const permissionRoles = value

            const hasRole = roles.some(role => permissionRoles.includes(role))

            if (!hasRole) {
                el.parentNode && el.parentNode.removeChild(el)
            }
        } else {
            throw new Error(`need roles! Like v-permission="['admin','editor']"`)
        }
    }
} 