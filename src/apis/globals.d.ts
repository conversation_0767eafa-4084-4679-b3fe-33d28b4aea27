/* tslint:disable */
/* eslint-disable */
/**
 * 交通强国-船舶与海洋工程 - version 1.0.0
 *
 *
 *
 * OpenAPI version: 3.0.0
 *
 *
 * NOTE: This file is auto generated by the alova's vscode plugin.
 *
 * https://alova.js.org/devtools/vscode
 *
 * **Do not edit the file manually.**
 */
import type { Alova, AlovaMethodCreateConfig, AlovaGenerics, Method } from 'alova';
import type { $$userConfigMap, alovaInstance } from '.';
import type apiDefinitions from './apiDefinitions';

type CollapsedAlova = typeof alovaInstance;
type UserMethodConfigMap = typeof $$userConfigMap;

type Alova2MethodConfig<Responded> =
  CollapsedAlova extends Alova<
    AlovaGenerics<
      any,
      any,
      infer RequestConfig,
      infer Response,
      infer ResponseHeader,
      infer L1Cache,
      infer L2Cache,
      infer SE
    >
  >
    ? Omit<
        AlovaMethodCreateConfig<
          AlovaGenerics<Responded, any, RequestConfig, Response, ResponseHeader, L1Cache, L2Cache, SE>,
          any,
          Responded
        >,
        'params'
      >
    : never;

// Extract the return type of transform function that define in $$userConfigMap, if it not exists, use the default type.
type ExtractUserDefinedTransformed<
  DefinitionKey extends keyof typeof apiDefinitions,
  Default
> = DefinitionKey extends keyof UserMethodConfigMap
  ? UserMethodConfigMap[DefinitionKey]['transform'] extends (...args: any[]) => any
    ? Awaited<ReturnType<UserMethodConfigMap[DefinitionKey]['transform']>>
    : Default
  : Default;
type Alova2Method<
  Responded,
  DefinitionKey extends keyof typeof apiDefinitions,
  CurrentConfig extends Alova2MethodConfig<any>
> =
  CollapsedAlova extends Alova<
    AlovaGenerics<
      any,
      any,
      infer RequestConfig,
      infer Response,
      infer ResponseHeader,
      infer L1Cache,
      infer L2Cache,
      infer SE
    >
  >
    ? Method<
        AlovaGenerics<
          CurrentConfig extends undefined
            ? ExtractUserDefinedTransformed<DefinitionKey, Responded>
            : CurrentConfig['transform'] extends (...args: any[]) => any
              ? Awaited<ReturnType<CurrentConfig['transform']>>
              : ExtractUserDefinedTransformed<DefinitionKey, Responded>,
          any,
          RequestConfig,
          Response,
          ResponseHeader,
          L1Cache,
          L2Cache,
          SE
        >
      >
    : never;

declare global {
  interface Apis {
    general: {
      /**
       * ---
       *
       * [POST] 用户登录
       *
       * **path:** /shipstruct/user/login
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [title] 用户名
       *   // [required]
       *   userId: string
       *   // [title] 密码
       *   // [required]
       *   password: string
       *   // [title] 角色
       *   // ccs用户版本，传值ccs，外部用户版，默认不传
       *   role?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   code: string
       *   // [required]
       *   msg: string
       *   // [title] 登录token
       *   // 登录token，需要再接口header中携带
       *   // [required]
       *   data: string
       * }
       * ```
       */
      post_shipstruct_user_login<
        Config extends Alova2MethodConfig<{
          /**
           * [required]
           */
          code: string;
          /**
           * [required]
           */
          msg: string;
          /**
           * 登录token
           * ---
           * 登录token，需要再接口header中携带
           * [required]
           */
          data: string;
        }> & {
          data: {
            /**
             * 用户名
             * ---
             * [required]
             */
            userId: string;
            /**
             * 密码
             * ---
             * [required]
             */
            password: string;
            /**
             * 角色
             * ---
             * ccs用户版本，传值ccs，外部用户版，默认不传
             */
            role?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * [required]
           */
          code: string;
          /**
           * [required]
           */
          msg: string;
          /**
           * 登录token
           * ---
           * 登录token，需要再接口header中携带
           * [required]
           */
          data: string;
        },
        'general.post_shipstruct_user_login',
        Config
      >;
      /**
       * ---
       *
       * [GET] 获取用户信息
       *
       * **path:** /shipstruct/user/getUserInfo
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   code: string
       *   // [required]
       *   msg: string
       *   // [required]
       *   data: {
       *     // [required]
       *     user: {
       *       // [title] 用户名
       *       // [required]
       *       userName: string
       *       // [title] 所属组织
       *       // [required]
       *       comGroup: string
       *     }
       *     // [required]
       *     record: {
       *       // [title] 最近一次登录时间
       *       // 格式时间戳，需要改为yyyyMMdd hh:mm:ss
       *       // [required]
       *       loginTime: string
       *     }
       *   }
       * }
       * ```
       */
      get_shipstruct_user_getuserinfo<
        Config extends Alova2MethodConfig<{
          /**
           * [required]
           */
          code: string;
          /**
           * [required]
           */
          msg: string;
          /**
           * [required]
           */
          data: {
            /**
             * [required]
             */
            user: {
              /**
               * 用户名
               * ---
               * [required]
               */
              userName: string;
              /**
               * 所属组织
               * ---
               * [required]
               */
              comGroup: string;
            };
            /**
             * [required]
             */
            record: {
              /**
               * 最近一次登录时间
               * ---
               * 格式时间戳，需要改为yyyyMMdd hh:mm:ss
               * [required]
               */
              loginTime: string;
            };
          };
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          /**
           * [required]
           */
          code: string;
          /**
           * [required]
           */
          msg: string;
          /**
           * [required]
           */
          data: {
            /**
             * [required]
             */
            user: {
              /**
               * 用户名
               * ---
               * [required]
               */
              userName: string;
              /**
               * 所属组织
               * ---
               * [required]
               */
              comGroup: string;
            };
            /**
             * [required]
             */
            record: {
              /**
               * 最近一次登录时间
               * ---
               * 格式时间戳，需要改为yyyyMMdd hh:mm:ss
               * [required]
               */
              loginTime: string;
            };
          };
        },
        'general.get_shipstruct_user_getuserinfo',
        Config
      >;
      /**
       * ---
       *
       * [POST] 修改用户信息
       *
       * **path:** /shipstruct/user/changeUserInfo
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [title] 新用户名
       *   // [required]
       *   userName: string
       *   // [title] 所属组织
       *   // [required]
       *   comGroup: string
       *   // [title] 新密码
       *   // [required]
       *   password: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   code: string
       *   // [required]
       *   msg: string
       *   // [required]
       *   data: object
       * }
       * ```
       */
      post_shipstruct_user_changeuserinfo<
        Config extends Alova2MethodConfig<{
          /**
           * [required]
           */
          code: string;
          /**
           * [required]
           */
          msg: string;
          /**
           * [required]
           */
          data: object;
        }> & {
          data: {
            /**
             * 新用户名
             * ---
             * [required]
             */
            userName: string;
            /**
             * 所属组织
             * ---
             * [required]
             */
            comGroup: string;
            /**
             * 新密码
             * ---
             * [required]
             */
            password: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * [required]
           */
          code: string;
          /**
           * [required]
           */
          msg: string;
          /**
           * [required]
           */
          data: object;
        },
        'general.post_shipstruct_user_changeuserinfo',
        Config
      >;
      /**
       * ---
       *
       * [POST] 添加船舶信息
       *
       * **path:** /shipstruct/ship/struct/addShip
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [required]
       *   shipName: string
       *   // [required]
       *   shipOwner: string
       *   // [required]
       *   shipType: string
       *   // [required]
       *   area: string
       *   // [required]
       *   shipYard: string
       *   // [required]
       *   imo: string
       *   shipLoa?: number
       *   shipLpp?: number
       *   shipLrule?: number
       *   beamArch?: number
       *   moldDepth?: number
       *   moldWidth?: number
       *   structDraft?: number
       *   designDraft?: number
       *   dySwbmHogg?: number
       *   dySwbmSagg?: number
       *   staSwbmHogg?: number
       *   staSwbmSagg?: number
       *   dwt?: number
       *   lsw?: number
       *   ndw?: number
       *   csa?: string
       *   csm?: string
       *   keelDate?: string
       *   features?: {
       *     // [required]
       *     特征1: string
       *     // [required]
       *     特征2: string
       *   } | null
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   code: string
       *   // [required]
       *   msg: string
       *   // [title] 返回船舶id
       *   data?: string
       * }
       * ```
       */
      post_shipstruct_ship_struct_addship<
        Config extends Alova2MethodConfig<{
          /**
           * [required]
           */
          code: string;
          /**
           * [required]
           */
          msg: string;
          /**
           * 返回船舶id
           * ---
           */
          data?: string;
        }> & {
          data: {
            /**
             * [required]
             */
            shipName: string;
            /**
             * [required]
             */
            shipOwner: string;
            /**
             * [required]
             */
            shipType: string;
            /**
             * [required]
             */
            area: string;
            /**
             * [required]
             */
            shipYard: string;
            /**
             * [required]
             */
            imo: string;
            shipLoa?: number;
            shipLpp?: number;
            shipLrule?: number;
            beamArch?: number;
            moldDepth?: number;
            moldWidth?: number;
            structDraft?: number;
            designDraft?: number;
            dySwbmHogg?: number;
            dySwbmSagg?: number;
            staSwbmHogg?: number;
            staSwbmSagg?: number;
            dwt?: number;
            lsw?: number;
            ndw?: number;
            csa?: string;
            csm?: string;
            keelDate?: string;
            features?: {
              /**
               * [required]
               */
              特征1: string;
              /**
               * [required]
               */
              特征2: string;
            } | null;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * [required]
           */
          code: string;
          /**
           * [required]
           */
          msg: string;
          /**
           * 返回船舶id
           * ---
           */
          data?: string;
        },
        'general.post_shipstruct_ship_struct_addship',
        Config
      >;
      /**
       * ---
       *
       * [POST] 添加船舶装置工况
       *
       * **path:** /shipstruct/ship/struct/addLc
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [title] 船舶id
       *   // 添加船舶信息后，返回的data id
       *   // [required]
       *   id: number
       *   // [title] 许用值列表
       *   // map格式的装载工况许用值，key为：许用值_工况1_其他描述，value为具体指
       *   // [required]
       *   features: {
       *     // [required]
       *     许用值1: string
       *     // [required]
       *     许用值2: string
       *   }
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   code: string
       *   // [required]
       *   msg: string
       * }
       * ```
       */
      post_shipstruct_ship_struct_addlc<
        Config extends Alova2MethodConfig<{
          /**
           * [required]
           */
          code: string;
          /**
           * [required]
           */
          msg: string;
        }> & {
          data: {
            /**
             * 船舶id
             * ---
             * 添加船舶信息后，返回的data id
             * [required]
             */
            id: number;
            /**
             * 许用值列表
             * ---
             * map格式的装载工况许用值，key为：许用值_工况1_其他描述，value为具体指
             * [required]
             */
            features: {
              /**
               * [required]
               */
              许用值1: string;
              /**
               * [required]
               */
              许用值2: string;
            };
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * [required]
           */
          code: string;
          /**
           * [required]
           */
          msg: string;
        },
        'general.post_shipstruct_ship_struct_addlc',
        Config
      >;
      /**
       * ---
       *
       * [POST] 上传船舶图纸
       *
       * **path:** /shipstruct/ship/uploadReport
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   file?: Blob
       *   // 船舶id
       *   shipId?: number
       *   // 图纸类型
       *   type?: string
       *   // 摘要描述
       *   subtitle?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      post_shipstruct_ship_uploadreport<
        Config extends Alova2MethodConfig<object> & {
          data: {
            file?: Blob;
            /**
             * 船舶id
             */
            shipId?: number;
            /**
             * 图纸类型
             */
            type?: string;
            /**
             * 摘要描述
             */
            subtitle?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<object, 'general.post_shipstruct_ship_uploadreport', Config>;
      /**
       * ---
       *
       * [POST] 导出船舶信息
       *
       * **path:** /shipstruct/ship/admin/export
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [title] 船名
       *   shipName?: string
       *   // [title] 船东
       *   shipOwner?: string
       *   // [title] 船舶类型
       *   shipType?: string
       *   // [title] 航区
       *   area?: string
       *   // [title] 船厂
       *   shipYard?: string
       *   // [title] imo号
       *   imo?: string
       *   // [title] 最小船长
       *   minShipLoa?: number
       *   // [title] 最大船长
       *   maxShipLoa?: number
       *   // [title] 最小排水量
       *   minDwt?: number
       *   // [title] 最大排水量
       *   maxDwt?: number
       *   // [title] 最小型宽
       *   minMoldWidth?: number
       *   maxMoldWidth?: number
       *   // [title] 最小型深
       *   minMoldDepth?: number
       *   maxMoldDepth?: number
       *   // [title] 最小结构吃水
       *   minStructDraft?: number
       *   maxStructDraft?: number
       *   // [title] 最小设计吃水
       *   minDesignDraft?: number
       *   maxDesignDraft?: number
       *   // [title] 入级符号-结构和设备
       *   csa?: string
       *   // [title] 入级符号-机械轮机和电气
       *   csm?: string
       *   // [title] 特征
       *   features?: {
       *     特征1?: string
       *     特征2?: string
       *   }
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      post_shipstruct_ship_admin_export<
        Config extends Alova2MethodConfig<object> & {
          data: {
            /**
             * 船名
             * ---
             */
            shipName?: string;
            /**
             * 船东
             * ---
             */
            shipOwner?: string;
            /**
             * 船舶类型
             * ---
             */
            shipType?: string;
            /**
             * 航区
             * ---
             */
            area?: string;
            /**
             * 船厂
             * ---
             */
            shipYard?: string;
            /**
             * imo号
             * ---
             */
            imo?: string;
            /**
             * 最小船长
             * ---
             */
            minShipLoa?: number;
            /**
             * 最大船长
             * ---
             */
            maxShipLoa?: number;
            /**
             * 最小排水量
             * ---
             */
            minDwt?: number;
            /**
             * 最大排水量
             * ---
             */
            maxDwt?: number;
            /**
             * 最小型宽
             * ---
             */
            minMoldWidth?: number;
            maxMoldWidth?: number;
            /**
             * 最小型深
             * ---
             */
            minMoldDepth?: number;
            maxMoldDepth?: number;
            /**
             * 最小结构吃水
             * ---
             */
            minStructDraft?: number;
            maxStructDraft?: number;
            /**
             * 最小设计吃水
             * ---
             */
            minDesignDraft?: number;
            maxDesignDraft?: number;
            /**
             * 入级符号-结构和设备
             * ---
             */
            csa?: string;
            /**
             * 入级符号-机械轮机和电气
             * ---
             */
            csm?: string;
            /**
             * 特征
             * ---
             */
            features?: {
              特征1?: string;
              特征2?: string;
            };
          };
        }
      >(
        config: Config
      ): Alova2Method<object, 'general.post_shipstruct_ship_admin_export', Config>;
      /**
       * ---
       *
       * [GET] 导出记录
       *
       * **path:** /shipstruct/admin/ship/exportrecord/getRecords
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   exportTimeStart?: string
       *   exportTimeEnd?: string
       * }
       * ```
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [title] 导出开始时间
       *   // [required]
       *   exportTimeStart: string
       *   // [title] 导出结束时间
       *   // [required]
       *   exportTimeEnd: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   code: string
       *   // [required]
       *   msg: string
       *   // [required]
       *   data: {
       *     // [title] 总数量
       *     // [required]
       *     total: string
       *     // [title] 总页数
       *     // [required]
       *     pageNum: string
       *     // [title] 记录列表
       *     // [required]
       *     rows: Array<{
       *       // [required]
       *       id: string
       *       // [title] 导出时间
       *       exportTime?: string
       *       // [title] 验证码
       *       verifyCode?: string
       *       // [title] 文件
       *       filePath?: string
       *     }>
       *   }
       * }
       * ```
       */
      get_shipstruct_admin_ship_exportrecord_getrecords<
        Config extends Alova2MethodConfig<{
          /**
           * [required]
           */
          code: string;
          /**
           * [required]
           */
          msg: string;
          /**
           * [required]
           */
          data: {
            /**
             * 总数量
             * ---
             * [required]
             */
            total: string;
            /**
             * 总页数
             * ---
             * [required]
             */
            pageNum: string;
            /**
             * 记录列表
             * ---
             * [required]
             */
            rows: Array<{
              /**
               * [required]
               */
              id: string;
              /**
               * 导出时间
               * ---
               */
              exportTime?: string;
              /**
               * 验证码
               * ---
               */
              verifyCode?: string;
              /**
               * 文件
               * ---
               */
              filePath?: string;
            }>;
          };
        }> & {
          params: {
            exportTimeStart?: string;
            exportTimeEnd?: string;
          };
          data: {
            /**
             * 导出开始时间
             * ---
             * [required]
             */
            exportTimeStart: string;
            /**
             * 导出结束时间
             * ---
             * [required]
             */
            exportTimeEnd: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * [required]
           */
          code: string;
          /**
           * [required]
           */
          msg: string;
          /**
           * [required]
           */
          data: {
            /**
             * 总数量
             * ---
             * [required]
             */
            total: string;
            /**
             * 总页数
             * ---
             * [required]
             */
            pageNum: string;
            /**
             * 记录列表
             * ---
             * [required]
             */
            rows: Array<{
              /**
               * [required]
               */
              id: string;
              /**
               * 导出时间
               * ---
               */
              exportTime?: string;
              /**
               * 验证码
               * ---
               */
              verifyCode?: string;
              /**
               * 文件
               * ---
               */
              filePath?: string;
            }>;
          };
        },
        'general.get_shipstruct_admin_ship_exportrecord_getrecords',
        Config
      >;
      /**
       * ---
       *
       * [POST] 下载导出文件
       *
       * **path:** /shipstruct/admin/ship/exportrecord/download
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 导出记录id
       *   id?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      post_shipstruct_admin_ship_exportrecord_download<
        Config extends Alova2MethodConfig<object> & {
          params: {
            /**
             * 导出记录id
             */
            id?: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<object, 'general.post_shipstruct_admin_ship_exportrecord_download', Config>;
      /**
       * ---
       *
       * [GET] 获取船舶详情
       *
       * **path:** /shipstruct/ship/struct/getShipDetail
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 船舶id
       *   id?: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      get_shipstruct_ship_struct_getshipdetail<
        Config extends Alova2MethodConfig<object> & {
          params: {
            /**
             * 船舶id
             */
            id?: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<object, 'general.get_shipstruct_ship_struct_getshipdetail', Config>;
      /**
       * ---
       *
       * [POST] 获取船舶关联图纸，报告等上传附件信息
       *
       * **path:** /shipstruct/ship/getReports
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [title] 船舶id
       *   // [required]
       *   shipId: number
       *   // [title] 图纸类型
       *   // 船舶结构、计算模型、评估报告、装载信息
       *   // [required]
       *   reportType: string
       *   // [title] 图纸摘要
       *   // [required]
       *   subTitle: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      post_shipstruct_ship_getreports<
        Config extends Alova2MethodConfig<object> & {
          data: {
            /**
             * 船舶id
             * ---
             * [required]
             */
            shipId: number;
            /**
             * 图纸类型
             * ---
             * 船舶结构、计算模型、评估报告、装载信息
             * [required]
             */
            reportType: string;
            /**
             * 图纸摘要
             * ---
             * [required]
             */
            subTitle: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<object, 'general.post_shipstruct_ship_getreports', Config>;
      /**
       * ---
       *
       * [POST] 数据导入
       *
       * **path:** /shipstruct/ship/import
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // zip解压密码
       *   verifyCode?: string
       * }
       * ```
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   file?: Blob
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      post_shipstruct_ship_import<
        Config extends Alova2MethodConfig<object> & {
          params: {
            /**
             * zip解压密码
             */
            verifyCode?: string;
          };
          data: {
            file?: Blob;
          };
        }
      >(
        config: Config
      ): Alova2Method<object, 'general.post_shipstruct_ship_import', Config>;
      /**
       * ---
       *
       * [POST] 数据申请
       *
       * **path:** /shipstruct/ship/apply
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [title] 收件人
       *   // [required]
       *   toAccount: string
       *   // [title] 邮件主题
       *   // [required]
       *   subject: string
       *   // [title] 邮件内容
       *   // [required]
       *   content: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      post_shipstruct_ship_apply<
        Config extends Alova2MethodConfig<object> & {
          data: {
            /**
             * 收件人
             * ---
             * [required]
             */
            toAccount: string;
            /**
             * 邮件主题
             * ---
             * [required]
             */
            subject: string;
            /**
             * 邮件内容
             * ---
             * [required]
             */
            content: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<object, 'general.post_shipstruct_ship_apply', Config>;
      /**
       * ---
       *
       * [POST] 按船舶类型统计
       *
       * **path:** /shipstruct/ship/struct/statisByType
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      post_shipstruct_ship_struct_statisbytype<Config extends Alova2MethodConfig<object>>(
        config?: Config
      ): Alova2Method<object, 'general.post_shipstruct_ship_struct_statisbytype', Config>;
      /**
       * ---
       *
       * [POST] 获取船舶列表
       *
       * **path:** /shipstruct/ship/struct/getAllShips
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 当前页码，不设置页数据大小。默认20，不允许调整
       *   pageNo?: string
       * }
       * ```
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [required]
       *   shipName: string
       *   // [required]
       *   shipOwner: string
       *   // [required]
       *   shipType: string
       *   // [required]
       *   area: string
       *   // [required]
       *   shipYard: string
       *   // [required]
       *   imo: string
       *   // [required]
       *   minShipLoa: number
       *   // [required]
       *   maxShipLoa: number
       *   // [required]
       *   minDwt: number
       *   // [required]
       *   maxDwt: number
       *   // [required]
       *   minMoldWidth: number
       *   // [required]
       *   maxMoldWidth: number
       *   // [required]
       *   minMoldDepth: number
       *   // [required]
       *   maxMoldDepth: number
       *   // [required]
       *   minStructDraft: number
       *   // [required]
       *   maxStructDraft: number
       *   // [required]
       *   minDesignDraft: number
       *   // [required]
       *   maxDesignDraft: number
       *   // [required]
       *   csa: string
       *   // [required]
       *   csm: string
       *   // [required]
       *   features: {
       *     // [required]
       *     特征1: string
       *     // [required]
       *     特征2: string
       *   }
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   code: string
       *   // [required]
       *   msg: string
       *   data?: {
       *     // [title] 数据总数
       *     // [required]
       *     total: number
       *     // [title] 总页数
       *     // [required]
       *     pageNum: number
       *     // [required]
       *     rows: Array<{
       *       // [required]
       *       shipName: string
       *       // [required]
       *       shipOwner: string
       *     }>
       *   }
       * }
       * ```
       */
      post_shipstruct_ship_struct_getallships<
        Config extends Alova2MethodConfig<{
          /**
           * [required]
           */
          code: string;
          /**
           * [required]
           */
          msg: string;
          data?: {
            /**
             * 数据总数
             * ---
             * [required]
             */
            total: number;
            /**
             * 总页数
             * ---
             * [required]
             */
            pageNum: number;
            /**
             * [required]
             */
            rows: Array<{
              /**
               * [required]
               */
              shipName: string;
              /**
               * [required]
               */
              shipOwner: string;
            }>;
          };
        }> & {
          params: {
            /**
             * 当前页码，不设置页数据大小。默认20，不允许调整
             */
            pageNo?: string;
          };
          data: {
            /**
             * [required]
             */
            shipName: string;
            /**
             * [required]
             */
            shipOwner: string;
            /**
             * [required]
             */
            shipType: string;
            /**
             * [required]
             */
            area: string;
            /**
             * [required]
             */
            shipYard: string;
            /**
             * [required]
             */
            imo: string;
            /**
             * [required]
             */
            minShipLoa: number;
            /**
             * [required]
             */
            maxShipLoa: number;
            /**
             * [required]
             */
            minDwt: number;
            /**
             * [required]
             */
            maxDwt: number;
            /**
             * [required]
             */
            minMoldWidth: number;
            /**
             * [required]
             */
            maxMoldWidth: number;
            /**
             * [required]
             */
            minMoldDepth: number;
            /**
             * [required]
             */
            maxMoldDepth: number;
            /**
             * [required]
             */
            minStructDraft: number;
            /**
             * [required]
             */
            maxStructDraft: number;
            /**
             * [required]
             */
            minDesignDraft: number;
            /**
             * [required]
             */
            maxDesignDraft: number;
            /**
             * [required]
             */
            csa: string;
            /**
             * [required]
             */
            csm: string;
            /**
             * [required]
             */
            features: {
              /**
               * [required]
               */
              特征1: string;
              /**
               * [required]
               */
              特征2: string;
            };
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * [required]
           */
          code: string;
          /**
           * [required]
           */
          msg: string;
          data?: {
            /**
             * 数据总数
             * ---
             * [required]
             */
            total: number;
            /**
             * 总页数
             * ---
             * [required]
             */
            pageNum: number;
            /**
             * [required]
             */
            rows: Array<{
              /**
               * [required]
               */
              shipName: string;
              /**
               * [required]
               */
              shipOwner: string;
            }>;
          };
        },
        'general.post_shipstruct_ship_struct_getallships',
        Config
      >;
      /**
       * ---
       *
       * [POST] 未命名接口
       *
       * **path:** /predict
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = object
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = object
       * ```
       */
      post_predict<
        Config extends Alova2MethodConfig<object> & {
          data: object;
        }
      >(
        config: Config
      ): Alova2Method<object, 'general.post_predict', Config>;
      /**
       * ---
       *
       * [GET] 获取最新添加的船舶信息
       *
       * **path:** /shipstruct/ship/getLatestShip
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   code: string
       *   // [required]
       *   msg: string
       *   // [required]
       *   data: {
       *     // [title] 船舶基本信息
       *     // 和船舶详情页一致
       *     // [required]
       *     ship: object
       *     // [title] 报告图纸
       *     // 和概要信息一致
       *     // [required]
       *     report: object
       *     // [title] 许用值
       *     // 和概要信息一致
       *     // [required]
       *     allowStress: object
       *   }
       * }
       * ```
       */
      get_shipstruct_ship_getlatestship<
        Config extends Alova2MethodConfig<{
          /**
           * [required]
           */
          code: string;
          /**
           * [required]
           */
          msg: string;
          /**
           * [required]
           */
          data: {
            /**
             * 船舶基本信息
             * ---
             * 和船舶详情页一致
             * [required]
             */
            ship: object;
            /**
             * 报告图纸
             * ---
             * 和概要信息一致
             * [required]
             */
            report: object;
            /**
             * 许用值
             * ---
             * 和概要信息一致
             * [required]
             */
            allowStress: object;
          };
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          /**
           * [required]
           */
          code: string;
          /**
           * [required]
           */
          msg: string;
          /**
           * [required]
           */
          data: {
            /**
             * 船舶基本信息
             * ---
             * 和船舶详情页一致
             * [required]
             */
            ship: object;
            /**
             * 报告图纸
             * ---
             * 和概要信息一致
             * [required]
             */
            report: object;
            /**
             * 许用值
             * ---
             * 和概要信息一致
             * [required]
             */
            allowStress: object;
          };
        },
        'general.get_shipstruct_ship_getlatestship',
        Config
      >;
      /**
       * ---
       *
       * [GET] 详情页船舶概要信息
       *
       * **path:** /shipstruct/ship/getSummary
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // 船舶id
       *   shipId?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   code: string
       *   // [required]
       *   msg: string
       *   // [required]
       *   data: {
       *     // [required]
       *     report: Array<{
       *       // [title] 图纸摘要信息
       *       // 如：船舶横剖面图
       *       // [required]
       *       subTitle: string
       *       // [title] 图纸本地路径
       *       // [required]
       *       filePath: string
       *     }>
       *     // [required]
       *     allowStress: Array<{
       *       // [required]
       *       工况1_临界许用值: string
       *       // [required]
       *       工况1_最小许用值: string
       *     }>
       *   }
       * }
       * ```
       */
      get_shipstruct_ship_getsummary<
        Config extends Alova2MethodConfig<{
          /**
           * [required]
           */
          code: string;
          /**
           * [required]
           */
          msg: string;
          /**
           * [required]
           */
          data: {
            /**
             * [required]
             */
            report: Array<{
              /**
               * 图纸摘要信息
               * ---
               * 如：船舶横剖面图
               * [required]
               */
              subTitle: string;
              /**
               * 图纸本地路径
               * ---
               * [required]
               */
              filePath: string;
            }>;
            /**
             * [required]
             */
            allowStress: Array<{
              /**
               * [required]
               */
              工况1_临界许用值: string;
              /**
               * [required]
               */
              工况1_最小许用值: string;
            }>;
          };
        }> & {
          params: {
            /**
             * 船舶id
             */
            shipId?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * [required]
           */
          code: string;
          /**
           * [required]
           */
          msg: string;
          /**
           * [required]
           */
          data: {
            /**
             * [required]
             */
            report: Array<{
              /**
               * 图纸摘要信息
               * ---
               * 如：船舶横剖面图
               * [required]
               */
              subTitle: string;
              /**
               * 图纸本地路径
               * ---
               * [required]
               */
              filePath: string;
            }>;
            /**
             * [required]
             */
            allowStress: Array<{
              /**
               * [required]
               */
              工况1_临界许用值: string;
              /**
               * [required]
               */
              工况1_最小许用值: string;
            }>;
          };
        },
        'general.get_shipstruct_ship_getsummary',
        Config
      >;
    };
  }

  var Apis: Apis;
}
