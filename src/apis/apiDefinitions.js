/// <reference types='./globals.d.ts' />
/* tslint:disable */
/* eslint-disable */
/**
 * 交通强国-船舶与海洋工程 - version 1.0.0
 *
 *
 *
 * OpenAPI version: 3.0.0
 *
 *
 * NOTE: This file is auto generated by the alova's vscode plugin.
 *
 * https://alova.js.org/devtools/vscode
 *
 * **Do not edit the file manually.**
 */
export default {
  'general.post_shipstruct_user_login': ['POST', '/shipstruct/user/login'],
  'general.get_shipstruct_user_getuserinfo': ['GET', '/shipstruct/user/getUserInfo'],
  'general.post_shipstruct_user_changeuserinfo': ['POST', '/shipstruct/user/changeUserInfo'],
  'general.post_shipstruct_ship_struct_addship': ['POST', '/shipstruct/ship/struct/addShip'],
  'general.post_shipstruct_ship_struct_addlc': ['POST', '/shipstruct/ship/struct/addLc'],
  'general.post_shipstruct_ship_uploadreport': ['POST', '/shipstruct/ship/uploadReport'],
  'general.post_shipstruct_ship_admin_export': ['POST', '/shipstruct/ship/admin/export'],
  'general.get_shipstruct_admin_ship_exportrecord_getrecords': [
    'GET',
    '/shipstruct/admin/ship/exportrecord/getRecords'
  ],
  'general.post_shipstruct_admin_ship_exportrecord_download': ['POST', '/shipstruct/admin/ship/exportrecord/download'],
  'general.get_shipstruct_ship_struct_getshipdetail': ['GET', '/shipstruct/ship/struct/getShipDetail'],
  'general.post_shipstruct_ship_getreports': ['POST', '/shipstruct/ship/getReports'],
  'general.post_shipstruct_ship_import': ['POST', '/shipstruct/ship/import'],
  'general.post_shipstruct_ship_apply': ['POST', '/shipstruct/ship/apply'],
  'general.post_shipstruct_ship_struct_statisbytype': ['POST', '/shipstruct/ship/struct/statisByType'],
  'general.post_shipstruct_ship_struct_getallships': ['POST', '/shipstruct/ship/struct/getAllShips'],
  'general.post_predict': ['POST', '/predict'],
  'general.get_shipstruct_ship_getlatestship': ['GET', '/shipstruct/ship/getLatestShip'],
  'general.get_shipstruct_ship_getsummary': ['GET', '/shipstruct/ship/getSummary']
};
