import { create<PERSON>lova } from 'alova';
import fetchAdapter from 'alova/fetch';
import vueHook from 'alova/vue';
import { createApis, withConfigType } from './createApis';
import { axiosRequestAdapter } from '@alova/adapter-axios';
import router from '@/router';
console.log(import.meta.env, 'import.meta.env')
// 从响应头中获取文件名
function getFileNameFromResponse(response) {
  const contentDisposition = response.headers['content-disposition'] || response.headers['Content-Disposition'];
  if (contentDisposition) {
    // 从 Content-Disposition 头中提取文件名
    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
    const matches = filenameRegex.exec(contentDisposition);
    if (matches != null && matches[1]) {
      // 去除引号
      let filename = matches[1].replace(/['"]/g, '');
      // 处理 URL 编码的文件名
      try {
        return decodeURIComponent(filename);
      } catch (e) {
        return filename;
      }
    }
  }
  // 如果无法从响应头获取文件名，则生成一个默认文件名
  const url = response.config.url;
  const urlParts = url.split('/');
  return urlParts[urlParts.length - 1] || 'download';
}
export const alovaInstance = createAlova({
  baseURL: import.meta.env.MODE === 'production' ? import.meta.env.VITE_SERVER : import.meta.env.VITE_BASE_API,
  statesHook: vueHook,
  requestAdapter: axiosRequestAdapter(),
  beforeRequest: method => {
    const config = method.config;
    // 从localStorage获取token
    const token = localStorage.getItem('token');
    // 如果有token则添加到请求头
    if (token) {
      // 根据后端API要求设置token格式
      config.headers['Authorization'] = token;
    }

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: new Date().getTime()
      }
    }

    return config;
  },
  responded: (response, method) => {
    // 处理 blob 类型的响应
    if (response.config.responseType === 'blob') {
      console.log(response.data, 'response.data')
      return {
        code: 200,
        data: response.data,
        // 从响应头中获取文件名
        fileName: getFileNameFromResponse(response)
      };
    }

    const res = response.data;

    // 如果返回的状态码不是200，说明接口请求有误
    if (res.code !== 200) {
      if (res.code === 403) {
        // 没有权限
        router.push('/401');
        ElMessage.warning('无此操作权限');
      } else if (res.code === 506 || res.code === 505 || res.code === 503) {
        ElMessage({
          message: res.msg || '登录已过期,请重新登录',
          type: 'error',
          duration: 5 * 1000
        });
        // 清除token并跳转登录页
        localStorage.removeItem('token');
        router.push('/login');
      } else {
        // 其他错误直接提示
        ElMessage({
          message: res.msg || '系统错误',
          type: 'error',
          duration: 5 * 1000
        });
      }
      return Promise.reject(new Error(res.msg || '系统错误'));
    } else {
      return res;
    }
  }
});

export const $$userConfigMap = withConfigType({});

/**
 * @type { Apis }
 */
const Apis = createApis(alovaInstance, $$userConfigMap);

export default Apis;
