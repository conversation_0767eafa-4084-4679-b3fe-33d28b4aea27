import { defineStore } from 'pinia';
import { getToken, setToken, removeToken } from '@/utils/auth';
import Cookies from 'js-cookie';

// 导入permission store以使用generateRoutes方法
import usePermissionStore from './permission';
import Apis from "@/apis";

const useUserStore = defineStore('user', {
    state: () => ({
        token: getToken(),
        userName: '',
        avatar: '',
        role: '',
        permissions: [],
        userId: '',
        email: '',
        comGroup: '',
        clientIp: '',
        loginTime: ''
    }),

    actions: {
        // 登录
        async login(userInfo) {
            try {
                const userId = userInfo.userId.trim();
                const password = userInfo.password
                // 根据实际API需要调整参数
                const loginParams = {
                    userId,
                    password,
                    role: "ccs"
                };
                const res = await Apis.general.post_shipstruct_user_login({ data: loginParams });
                setToken(res.data);
                this.token = res.data;
                // 如果响应中包含角色信息，则直接存储
                this.role = res.data.role || "";
                // 将角色信息存储到 cookie 中
                Cookies.set('role', this.role);

                // 登录成功后根据角色生成路由
                const permissionStore = usePermissionStore();
                await permissionStore.generateRoutes();
                // 登录成功后清除所有请求记录
                return Promise.resolve();
            } catch (error) {
                return Promise.reject(error);
            }
        },

        // 获取用户信息
        async getInfo() {
            try {
                const res = await Apis.general.get_shipstruct_user_getuserinfo({ data: {} });
                const { userId, userName, email, role, comGroup } = res.data.user;
                const { clientIp, loginTime } = res.data.record;
                this.role = role || '';
                this.userName = userName;
                this.userId = userId;
                this.email = email;
                this.comGroup = comGroup;
                this.clientIp = clientIp;
                this.loginTime = loginTime;
                console.log(userName, 'userName')
                // 将用户信息存储到 cookie 中
                Cookies.set('role', this.role);
                return Promise.resolve(res.data);
            } catch (error) {
                return Promise.reject(error);
            }
        },

        // 重置令牌
        resetToken() {
            this.token = '';
            this.role = '';
            this.permissions = [];
            this.userId = '';
            this.userName = '';
            this.email = '';
            this.comGroup = '';
            this.clientIp = '';
            this.loginTime = '';
            removeToken();
            // 移除所有 cookie
            Cookies.remove('role');
        },
    }
});

export default useUserStore; 