import { defineStore } from 'pinia';
import router from '@/router';
import { filterRoutes } from '@/utils/permission';
import { useUserStore } from '@/store'
import Cookies from 'js-cookie';

const usePermissionStore = defineStore('permission', {
    state: () => ({
        routes: [],
        addRoutes: [],
        defaultRoutes: [],
        topbarRoutes: [],
        sidebarRoutes: []
    }),

    actions: {
        setRoutes(routes) {
            this.addRoutes = routes;
            this.routes = router.options.routes.concat(routes);
        },

        generateRoutes() {
            return new Promise(resolve => {
                // 从localStorage获取用户角色
                const userStore = useUserStore()
                const roles = userStore.role || Cookies.get('role') || '';
                // 根据角色过滤路由
                const accessedRoutes = filterRoutes(router.options.routes, roles);
                // 清除所有现有路由
                router.getRoutes().forEach(route => {
                    // redirect 路由不删除  
                    if (route.name) {
                        router.removeRoute(route.name);
                    }
                });

                // 重新添加过滤后的路由
                accessedRoutes.forEach(route => {
                    router.addRoute(route);
                });
                this.routes = accessedRoutes;
                this.topbarRoutes = accessedRoutes;
                this.sidebarRoutes = accessedRoutes;
                resolve(accessedRoutes);
            });
        }
    }
});

export default usePermissionStore; 