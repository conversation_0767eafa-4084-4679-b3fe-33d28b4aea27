import { defineStore } from 'pinia';

const useAppStore = defineStore('app', {
    state: () => ({
        isCollapse: localStorage.getItem('sidebarCollapsed') === '1',
    }),

    actions: {
        toggleSidebarCollapse() {
            this.isCollapse = !this.isCollapse;
            // 保存折叠状态到本地存储
            localStorage.setItem('sidebarCollapsed', this.isCollapse ? '1' : '0');
        },

        setSidebarCollapse(status) {
            this.isCollapse = status;
            localStorage.setItem('sidebarCollapsed', this.isCollapse ? '1' : '0');
        }
    }
});


export default useAppStore; 