import { createApp } from 'vue'
import { createPinia } from 'pinia'
import Cookies from "js-cookie"
import 'uno.css'
import './assets/styles/index.scss'
// import './assets/styles/element/index.scss'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'
import router from './router'
import permission from './directives/permission'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import 'virtual:svg-icons-register'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ElementPlus, {
    // 支持 large、default、small
    size: Cookies.get("size") || "small",
    locale: zhCn,
})

// 注册权限指令
app.directive('permission', permission)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}

app.mount('#app') 