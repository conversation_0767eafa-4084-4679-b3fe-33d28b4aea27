<template>
  <div class="app-container">
    <!-- 顶部导航栏 -->
    <!-- <header class="app-header">
      <div class="header-left">
        <template v-if="isSmallScreen">
          <div class="app-icon" @click="goToHome" style="cursor: pointer">
            <img :src="LogoSvg" alt="Logo" class="svg-logo" />
          </div>
        </template>
        <template v-else>
          <h1 class="app-title" @click="goToHome" style="cursor: pointer">
            {{ title }}
          </h1>
        </template>
      </div>
      <div class="header-right">
        <el-dropdown trigger="click">
          <div class="user-dropdown">
            <el-avatar :size="32" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
            <span class="username">{{ currentUserName }}</span>
            <el-icon><arrow-down /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="goToUserInfo">个人信息</el-dropdown-item>
              <el-dropdown-item divided @click="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header> -->

    <div class="app-main">
      <!-- 左侧菜单 -->
      <transition name="sidebar">
        <el-aside :width="isCollapse ? '64px' : '220px'" class="app-aside">
          <el-scrollbar>
            <div class="collapse-btn-wrapper">
              <el-button type="primary" circle size="small" :class="['collapse-btn-outside', isCollapse ? 'm-x-[16px]' : '']" @click="toggleCollapse">
                <el-icon :size="16">
                  <component :is="isCollapse ? 'Expand' : 'Fold'" />
                </el-icon>
              </el-button>
            </div>
            <el-menu :default-active="activeLeftMenu" class="left-menu !mt-[30px]" :router="true" :collapse="isCollapse" :unique-opened="false" :collapse-transition="false">
              <recursive-menu :menu-list="allMenus" @menu-click="handleMenuClick" />
            </el-menu>
          </el-scrollbar>
        </el-aside>
      </transition>

      <!-- 主内容区 -->
      <el-main class="app-content">
        <div class="content-wrapper">
          <router-view v-slot="{ Component }">
            <transition name="fade-transform" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </el-main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore, useAppStore } from '@/store'
import { ElMessage } from 'element-plus'
import { Download, Document, ArrowDown } from '@element-plus/icons-vue'
import usePermissionStore from '@/store/modules/permission'
// 导入 SVG 图标
import LogoSvg from '@/assets/images/home/<USER>'
import RecursiveMenu from '@/components/Menu/RecursiveMenu.vue'
const permissionStore = usePermissionStore()
const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
const isCollapse = computed(() => appStore.isCollapse)
const activeTopMenu = ref('')
const activeLeftMenu = ref('')
const userStore = useUserStore()
const isSmallScreen = ref(false)
const currentUserName = computed(() => userStore.userName)
// 添加图标相关配置
const iconSize = ref('16px')
const title = import.meta.env.VITE_SYSTEM_NAME
// 获取图标颜色，优先使用菜单项自己的颜色设置，否则使用默认颜色
const getIconColor = item => {
  return item.meta?.iconColor || 'currentColor'
}

const handleMenuClick = menu => {
  const fullPath = menu.fullPath
  nextTick(() => {
    if (fullPath === route.path) {
      router.replace({
        path: `/redirect${fullPath}`
      })
    }
  })
}
// 切换侧边栏折叠状态
const toggleCollapse = () => {
  appStore.toggleSidebarCollapse()
}

// 获取所有菜单项（在侧边栏显示）
const allMenus = computed(() => {
  const routes = permissionStore.routes.find(route => route.path === '/')
  if (!routes || !routes.children) return []

  // 过滤掉隐藏的菜单项
  const visibleMenus = routes.children.filter(item => !item.hidden && !item.meta?.hidden)

  // 为每个菜单项添加fullPath属性
  const processMenus = (menus, basePath = '') => {
    return menus.map(menu => {
      // 创建菜单项的副本，避免修改原始对象
      const newMenu = { ...menu }

      // 构建完整路径
      newMenu.fullPath = menu.path.startsWith('/') ? menu.path : `${basePath}/${menu.path}`

      // 递归处理子菜单
      if (newMenu.children && newMenu.children.length > 0) {
        newMenu.children = processMenus(newMenu.children, newMenu.fullPath)
      }

      return newMenu
    })
  }

  return processMenus(visibleMenus)
})

// 查找当前路由对应的菜单项
const findActiveMenu = route => {
  const path = route.path
  const menuName = route?.meta?.menuName ?? '' // 带有查询参数的路由都会在meta中添加menuName，用来匹配

  // 递归查找匹配的菜单项
  const findMatchingPath = menus => {
    for (const menu of menus) {
      if (path === menu.fullPath || menuName === menu.name) {
        return menu.fullPath
      }
      if (menu.children && menu.children.length > 0) {
        const childMatch = findMatchingPath(menu.children)
        if (childMatch) return childMatch
      }
    }
    return null
  }

  // 尝试在所有菜单中查找匹配
  const match = findMatchingPath(allMenus.value)
  if (match) return match

  // 如果没有找到匹配，返回当前路径
  return path
}

// 监听路由变化，更新激活的菜单
watch(
  () => route.path,
  () => {
    // 更新侧边菜单激活状态
    activeLeftMenu.value = findActiveMenu(route)
  },
  { immediate: true }
)

// 创建一个函数来检查屏幕宽度
const checkScreenSize = () => {
  isSmallScreen.value = window.innerWidth < 970 // 可以根据需要调整此阈值
  appStore.setSidebarCollapse(isSmallScreen.value)
}

// 在组件挂载时初始化
onMounted(() => {
  // 初始检查
  checkScreenSize()

  // 添加窗口大小变化监听
  window.addEventListener('resize', checkScreenSize)

  activeLeftMenu.value = findActiveMenu(route)
})

// 在组件卸载前移除事件监听器（防止内存泄漏）
onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
})

// 跳转到用户信息页面
const goToUserInfo = () => {
  router.push('/personal-info')
}

// 退出登录
const logout = async () => {
  try {
    router.push('/login')
    ElMessage.success('退出登录成功')
  } catch (error) {
    router.push('/login')
  }
}

// 添加跳转到主页的方法
const goToHome = () => {
  router.push('/')
}
</script>

<style lang="scss" scoped>
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(to right, #0285a5, #4facfe 50%, #0285a5);
  color: white;
  padding: 0 20px;
  height: 60px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 20;
}

.header-left {
  display: flex;
  align-items: center;
}

.app-title {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: white;
  transition: opacity 0.3s; /* 添加过渡效果 */
}

.app-title:hover {
  opacity: 0.8; /* 添加悬停效果 */
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-dropdown,
.language-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: white;
}

.username {
  margin-left: 8px;
  color: white;
}

.language-dropdown {
  display: flex;
  align-items: center;
  gap: 5px;
  color: white;
}

.app-main {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: relative;
}

.app-aside {
  background-color: #0285a5;
  transition: all 0.3s ease-in-out;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform-origin: left;
  transform: translateX(0);
}

.left-menu {
  height: 100%;
  border-right: none;
  background-color: transparent;
}

.app-content {
  flex: 1;
  // overflow-y: auto;
  overflow-x: hidden;
  padding: 20px;
  background-color: #fff;
  transition: all 0.3s ease-in-out;
}

:deep(.el-menu) {
  border-right: none;
}

:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s;
}

:deep(.el-menu-item:hover),
:deep(.el-sub-menu__title:hover) {
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
}

:deep(.el-menu-item.is-active) {
  color: #ffd04b;
  background-color: rgba(255, 255, 255, 0.2);
}

/* 三级菜单样式 */
:deep(.el-menu--popup) {
  background-color: #0285a5;
}

:deep(.el-menu--popup .el-menu-item) {
  color: rgba(255, 255, 255, 0.8);
  background-color: #0285a5;
}

:deep(.el-menu--popup .el-menu-item:hover) {
  background-color: #036c88;
}

:deep(.el-menu--popup .el-menu-item.is-active) {
  color: #ffd04b;
  background-color: rgba(255, 255, 255, 0.2);
}

/* 顶部栏下拉菜单样式 */
:deep(.el-dropdown-menu__item) {
  padding: 8px 16px;
}

:deep(.el-dropdown-menu) {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 顶部栏图标颜色 */
:deep(.header-right .el-icon) {
  color: white;
}

/* 子菜单样式 */
:deep(.el-menu--vertical) {
  background-color: #0285a5 !important;
}

:deep(.el-menu--vertical .el-menu) {
  background-color: #0285a5 !important;
}

:deep(.el-menu--vertical .el-menu-item) {
  background-color: #0285a5 !important;
}

:deep(.el-sub-menu__title) {
  background-color: transparent !important;
}

:deep(.el-sub-menu .el-menu-item) {
  background-color: transparent !important;
}

/* 外部折叠按钮样式 */
.collapse-btn-wrapper {
  position: absolute;
  top: 8px;
  right: 3px;
  z-index: 100;
}

.collapse-btn-outside {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: none;
  background-color: transparent;
  color: #fff;
  transition: all 0.3s;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collapse-btn-outside:hover {
  transform: scale(1.1);
  background-color: #036c88;
  color: white;
}

// 添加侧边栏过渡动画
.sidebar-enter-active,
.sidebar-leave-active {
  transition: all 0.3s ease-out;
}

.sidebar-enter-from {
  transform: translateX(-100%);
  opacity: 0;
  width: 0;
}

.sidebar-leave-to {
  transform: translateX(-100%);
  opacity: 0;
  width: 0 !important;
  margin: 0;
  padding: 0;
}

.app-icon {
  font-size: 24px;
  color: white;
  margin: 0;
  transition: opacity 0.3s;
  display: flex;
  align-items: center;
}

.app-icon:hover {
  opacity: 0.8;
}

.svg-logo {
  height: 24px;
  width: auto;
  filter: brightness(0) invert(1); /* 使SVG变为白色 */
}

/* 添加内容区域的过渡动画样式 */
.content-wrapper {
  position: relative;
  min-height: 200px;
  height: 100%;
}

.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: opacity 0.3s ease;
}

.fade-transform-enter-from,
.fade-transform-leave-to {
  opacity: 0;
}

.export-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: white;
  gap: 5px;
}
</style>
