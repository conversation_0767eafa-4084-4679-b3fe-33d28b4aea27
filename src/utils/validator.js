/**
 * 数值验证器函数
 * 验证输入是否为有效数值
 * @param {Object} rule - 验证规则
 * @param {any} value - 要验证的值
 * @param {Function} callback - 回调函数
 * @param {Boolean} required - 是否必填，默认为true
 */
export const validateNumber = (rule, value, callback, required = true) => {
    if (value === undefined || value === null || value === '') {
        if (required) {
            callback(new Error('该字段不能为空'));
        } else {
            callback();
        }
        return;
    }

    if (isNaN(Number(value))) {
        callback(new Error('必须输入数值'));
    } else {
        callback();
    }
}; 