import axios from 'axios';
import { ElMessage, ElMessageBox } from 'element-plus';
import router from '@/router';

// 从响应头中获取文件名
function getFileNameFromResponse(response) {
    const contentDisposition = response.headers['content-disposition'] || response.headers['Content-Disposition'];
    if (contentDisposition) {
        // 从 Content-Disposition 头中提取文件名
        const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
        const matches = filenameRegex.exec(contentDisposition);
        if (matches != null && matches[1]) {
            // 去除引号
            let filename = matches[1].replace(/['"]/g, '');
            // 处理 URL 编码的文件名
            try {
                return decodeURIComponent(filename);
            } catch (e) {
                return filename;
            }
        }
    }
    // 如果无法从响应头获取文件名，则生成一个默认文件名
    const url = response.config.url;
    const urlParts = url.split('/');
    return urlParts[urlParts.length - 1] || 'download';
}

// 创建axios实例
const service = axios.create({
    baseURL: import.meta.env.VITE_BASE_API, // 从环境变量获取API基础URL
    timeout: 10000, // 请求超时时间
    headers: {
        'Content-Type': 'application/json;charset=utf-8'
    }
});

// 请求拦截器
service.interceptors.request.use(
    config => {

        // 从localStorage获取token
        const token = localStorage.getItem('token');
        // 如果有token则添加到请求头
        if (token) {
            // 根据后端API要求设置token格式
            config.headers['Authorization'] = token;
        }

        // 添加时间戳防止缓存
        if (config.method === 'get') {
            config.params = {
                ...config.params,
                _t: new Date().getTime()
            }
        }

        return config;
    },
    error => {
        console.error('请求错误:', error);
        return Promise.reject(error);
    }
);

// 响应拦截器
service.interceptors.response.use(
    response => {
        // 处理 blob 类型的响应
        if (response.config.responseType === 'blob') {
            return {
                code: 200,
                data: response.data,
                // 从响应头中获取文件名
                fileName: getFileNameFromResponse(response)
            };
        }

        const res = response.data;

        // 如果返回的状态码不是200，说明接口请求有误
        if (res.code !== 200) {
            if (res.code === 403) {
                // 没有权限
                router.push('/401');
                ElMessage.warning('无此操作权限');
            } else if (res.code === 506 || res.code === 505 || res.code === 503) {
                ElMessage({
                    message: '登录已过期,请重新登录',
                    type: 'error',
                    duration: 5 * 1000
                });
                // 清除token并跳转登录页
                localStorage.removeItem('token');
                router.push('/login');
            } else {
                // 其他错误直接提示
                ElMessage({
                    message: res.msg || '系统错误',
                    type: 'error',
                    duration: 5 * 1000
                });
            }

            return Promise.reject(new Error(res.msg || '系统错误'));
        } else {
            return res;
        }
    },
    error => {
        // 如果是取消请求的错误，不显示错误消息
        if (axios.isCancel(error)) {
            return Promise.reject(error);
        }
        // 处理网络错误
        let message = '网络错误，请稍后重试';

        if (error.response) {
            switch (error.response.status) {
                case 400:
                    message = '请求错误';
                    break;
                case 401:
                    message = '未授权，请重新登录';
                    // 清除token并跳转登录页
                    localStorage.removeItem('token');
                    router.push('/login');
                    break;
                case 403:
                    message = '拒绝访问';
                    router.push('/401');
                    ElMessage.warning(message);
                    break;
                case 404:
                    message = '请求地址出错';
                    break;
                case 408:
                    message = '请求超时';
                    break;
                case 500:
                    message = '服务器内部错误';
                    break;
                case 501:
                    message = '服务未实现';
                    break;
                case 502:
                    message = '网关错误';
                    break;
                case 503:
                    message = '服务不可用';
                    break;
                case 504:
                    message = '网关超时';
                    break;
                case 505:
                    message = 'HTTP版本不受支持';
                    break;
                default:
                    message = `未知错误(${error.response.status})`;
            }
        }
        return Promise.reject(message);
    }
);

// 不在路由切换时清除所有请求记录，而是让防抖机制自然处理

// 封装GET请求
export function get(url, params, options = {}) {
    return service({
        url,
        method: 'get',
        params,
        ...options
    });
}

// 封装POST请求
export function post(url, data, options = {}) {
    return service({
        url,
        method: 'post',
        data,
        ...options
    });
}

// 封装PUT请求
export function put(url, data, options = {}) {
    return service({
        url,
        method: 'put',
        data,
        ...options
    });
}

// 封装DELETE请求
export function del(url, params, options = {}) {
    return service({
        url,
        method: 'delete',
        params,
        ...options
    });
}

// 封装上传文件的请求
export function upload(url, formData, options = {}) {
    return service({
        url,
        method: 'post',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        ...options
    });
}

// 封装下载文件的请求
export function download(url, method = 'get', params, options = {}) {
    const downloadOptions = {
        url,
        method,
        responseType: 'blob',
        ...options
    };

    // 根据请求方法类型，设置相应的参数
    if (method.toLowerCase() === 'get') {
        downloadOptions.params = params;
    } else {
        downloadOptions.data = params;  // POST等其他方法使用data传递参数
    }

    // 如果提供了文件名，则使用它
    const fileName = options.fileName;

    return service(downloadOptions).then(res => {
        // 创建 Blob 对象
        const blob = new Blob([res.data]);

        // 创建下载链接
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);

        // 设置文件名
        link.download = fileName || res.fileName || 'download';

        // 触发点击事件下载文件
        document.body.appendChild(link);
        link.click();

        // 清理
        window.URL.revokeObjectURL(link.href);
        document.body.removeChild(link);

        return res;
    });
}

// 封装获取验证码图片的请求
export function captcha(url, params, options = {}) {
    return service({
        url,
        method: 'get',
        params,
        responseType: 'blob',
        ...options
    }).then(res => {
        // 创建图片 URL
        const blob = new Blob([res.data]);
        const imageUrl = window.URL.createObjectURL(blob);

        // 返回图片 URL 和原始响应
        return {
            imageUrl,
            blob: res.data
        };
    });
}

// 自定义请求方法，支持更多配置选项
export function request(config) {
    return service(config);
}

// 导出axios实例
export default service; 