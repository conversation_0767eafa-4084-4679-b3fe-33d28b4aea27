/**
 * 权限控制工具
 */

// 检查用户是否有访问某个路由的权限
export function hasPermission(route, roles) {
    if (route.meta && route.meta.roles) {
        // 如果路由定义了roles，检查用户角色是否匹配
        return route.meta.roles.includes(roles);
    } else {
        // 如果路由没有定义roles，则默认所有人都可以访问
        return true;
    }
}

// 根据用户角色过滤路由
export function filterRoutes(routes, roles) {
    const res = [];
    routes.forEach(route => {
        const tmp = { ...route };

        if (hasPermission(tmp, roles)) {
            if (tmp.children) {
                tmp.children = filterRoutes(tmp.children, roles);
            }
            res.push(tmp);
        }
    });
    return res;
}

// 获取用户信息并设置权限
export async function getUserInfoAndSetPermissions(api, store) {
    try {
        // 获取用户信息
        const response = await api.user.getUserInfo();
        const { roles, name, avatar } = response.data;

        // 验证返回的roles是否是数组
        if (!roles || roles.length <= 0) {
            throw new Error('getUserInfo: roles must be a non-null array!');
        }

        // 存储用户信息
        store.commit('user/SET_ROLES', roles);
        store.commit('user/SET_NAME', name);
        store.commit('user/SET_AVATAR', avatar);

        return { roles };
    } catch (error) {
        throw error;
    }
} 