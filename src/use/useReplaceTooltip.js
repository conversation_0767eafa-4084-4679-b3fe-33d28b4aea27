import { ref, onMounted, nextTick } from 'vue'

/**
 * 检测文本是否溢出并决定是否显示tooltip
 * @param {Object} options - 配置对象
 * @param {string} options.text - 要显示的文本内容
 * @returns {Object} - 包含tooltipsDisabled引用值和labelEl引用值
 */
export function useReplaceTooltip(options) {
    const labelEl = ref(null)
    const tooltipsDisabled = ref(false)
    const checkDisabelStatus = async (text) => {
        await nextTick();
        if (labelEl.value) {
            // 创建临时元素测量完整文本宽度
            const tempSpan = document.createElement("span");
            const computedStyle = window.getComputedStyle(labelEl.value);

            // 复制原始元素的样式
            tempSpan.style.font = computedStyle.font;
            tempSpan.style.fontSize = computedStyle.fontSize;
            tempSpan.style.fontWeight = computedStyle.fontWeight;
            tempSpan.style.letterSpacing = computedStyle.letterSpacing;

            // 设置临时元素样式
            tempSpan.style.position = "absolute";
            tempSpan.style.visibility = "hidden";
            tempSpan.style.whiteSpace = "nowrap";
            tempSpan.textContent = text;

            // 添加到DOM以测量
            document.body.appendChild(tempSpan);

            // 检测是否溢出
            const fullTextWidth = tempSpan.offsetWidth;
            const availableWidth = labelEl.value.clientWidth;
            tooltipsDisabled.value = fullTextWidth <= availableWidth;
            // 移除临时元素
            document.body.removeChild(tempSpan);
        }
    }
    onMounted(async () => {
        checkDisabelStatus(options.text)
    });

    return {
        tooltipsDisabled,
        labelEl,
        checkDisabelStatus
    }
} 