import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { viteMockServe } from 'vite-plugin-mock'
import path from 'path'
import { createHtmlPlugin } from 'vite-plugin-html'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import { getSystemName, getEnvVariables } from './scripts/index'
// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  const title = getSystemName(mode)
  const env = loadEnv(mode, process.cwd(), '')

  return {
    // define: {
    //     '__BUILD_MODE__': JSON.stringify(mode)
    // },
    base: './',
    plugins: [
      vue(),
      UnoCSS(),
      // 自动导入 Element Plus 组件
      AutoImport({
        resolvers: [
          ElementPlusResolver({
            importStyle: 'sass'
          })
        ],
        imports: ['vue', 'vue-router', 'pinia'],
        dts: 'src/auto-imports.d.ts'
      }),
      Components({
        resolvers: [
          ElementPlusResolver({
            importStyle: 'sass'
          })
        ],
        dts: 'src/components.d.ts'
      }),
      createSvgIconsPlugin({
        // 指定需要缓存的图标文件夹
        iconDirs: [path.resolve(process.cwd(), 'src/assets/icons')],
        // 指定symbolId格式
        symbolId: 'icon-[dir]-[name]',
        // 是否压缩
        svgoOptions: true
      }),
      createHtmlPlugin({
        minify: true,
        inject: {
          data: {
            title: title
            // buildMode: mode,
            // 可以添加更多变量
          }
        }
      })
    ],
    // 优化依赖预构建
    optimizeDeps: {
      include: ['vue', 'vue-router', 'pinia', 'element-plus/es', '@element-plus/icons-vue', 'echarts/core', 'echarts/charts', 'echarts/components', 'echarts/renderers']
    },
    // 构建优化
    build: {
      sourcemap: false,
      rollupOptions: {
        output: {
          manualChunks: {
            'vue-vendor': ['vue', 'vue-router', 'pinia'],
            'element-plus': ['element-plus', '@element-plus/icons-vue'],
            echarts: ['echarts/core', 'echarts/charts', 'echarts/components', 'echarts/renderers']
          }
        }
      },
      terserOptions: {
        compress: {
          drop_console: true, // 移除 console
          drop_debugger: true // 移除 debugger
        }
      }
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    },
    server: {
      port: 3001,
      host: '0.0.0.0',
      proxy: {
        '/apis': {
          // target: env.VITE_SERVER,
          target: 'http://localhost:9000',
          changeOrigin: true,
          rewrite: path => path.replace(/^\/apis/, '')
        }
      }
    },
    css: {
      // 配置 css modules 的行为
      modules: {
        localsConvention: 'camelCase' // 默认只支持驼峰，修改为同时支持横线和驼峰
      },
      preprocessorOptions: {
        // css预处理器 自动引入全局scss文件
        scss: {
          additionalData: `@use "@/assets/styles/element/index.scss" as *;`
        }
      },

      postcss: {
        plugins: [
          require('tailwindcss'),
          require('autoprefixer'),
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: atRule => {
                if (atRule.name === 'charset') atRule.remove()
              }
            }
          }
        ]
      }
    }
  }
})
