2025-08-05 14:49:12 jdbc[7]: exception
org.h2.jdbc.JdbcSQLDataException: Value too long for column "area CHARACTER VARYING(32)": "'Greater Coastal Service Restriction' (35)"; SQL statement:
insert into t_ship_struct (ship_name, ship_owner, ship_type, area, ship_yard, imo, ship_loa, ship_lpp, ship_lrule, beam_arch, mold_depth, mold_width, struct_draft, design_draft,
                                   dy_swbm_hogg, dy_swbm_sagg, sta_swbm_hogg, sta_swbm_sagg, dwt, lsw, ndw, csa, csm,
                                   keel_date, c_time, m_time)
        values (?, ?, ?, ?, ?, ?, ?, ?,
                ?, ?, ?, ?, ?, ?,
                ?, ?, ?, ?, ?, ?, ?, ?, ?,
                ?, ?, ?
               ) [22001-214]
2025-08-05 14:50:04 jdbc[7]: exception
org.h2.jdbc.JdbcSQLDataException: Value too long for column "area CHARACTER VARYING(32)": "'Greater Coastal Service Restriction' (35)"; SQL statement:
insert into t_ship_struct (ship_name, ship_owner, ship_type, area, ship_yard, imo, ship_loa, ship_lpp, ship_lrule, beam_arch, mold_depth, mold_width, struct_draft, design_draft,
                                   dy_swbm_hogg, dy_swbm_sagg, sta_swbm_hogg, sta_swbm_sagg, dwt, lsw, ndw, csa, csm,
                                   keel_date, c_time, m_time)
        values (?, ?, ?, ?, ?, ?, ?, ?,
                ?, ?, ?, ?, ?, ?,
                ?, ?, ?, ?, ?, ?, ?, ?, ?,
                ?, ?, ?
               ) [22001-214]
