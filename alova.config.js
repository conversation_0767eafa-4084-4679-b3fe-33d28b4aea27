// For more config detailed visit:
// https://alova.js.org/tutorial/getting-started/extension-integration
/**
 * @type { import('@alova/wormhole').Config }
 */
export default {
  generator: [
    {
      /**
       * file input. support:
       * 1. openapi json file url
       * 2. local file
       */
      input: './swagger.json',

      /**
       * input file platform. Currently only swagger is supported.
       * When this parameter is specified, the input field only needs to specify the document address without specifying the openapi file
       */
      platform: 'swagger',

      /**
       * output path of interface file and type file.
       * Multiple generators cannot have the same address, otherwise the generated code will overwrite each other.
       */
      output: 'src/apis'
    }
  ]


};
