{"name": "web", "version": "0.1.0", "description": "船舶与海洋工程结构数据分析及支持服务 - 基于 Vue 3 + Electron 的桌面应用程序", "author": "Your Name <<EMAIL>>", "private": true, "main": "electron/main.js", "scripts": {"dev": "vite", "build": "vite build", "dev:client": "cross-env NODE_ENV=development electron .", "dev:client-dist": "electron .", "build:app": "vite build && cross-env NODE_ENV=production & electron-builder build --config electron-builder.json", "build:cli-only": "cross-env NODE_ENV=production & electron-builder build --config electron-builder.json", "build:win": "vite build && electron-builder build --win --config electron-builder.json", "build:win-only": "electron-builder build --win --config electron-builder.json"}, "dependencies": {"@antv/g6": "^5.0.44", "@element-plus/icons-vue": "^2.3.1", "alova": "^3.3.4", "cross-env": "^7.0.3", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.10.4", "interactjs": "^1.10.27", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "mockjs": "^1.1.0", "moment": "^2.30.1", "nprogress": "^0.2.0", "pdfjs-dist": "2.16.105", "pinia": "^2.1.4", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@alova/adapter-axios": "^2.0.16", "@alova/wormhole": "^1.0.7", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.21", "axios": "^1.5.0", "cz-conventional-changelog": "^3.3.0", "electron": "^37.2.3", "electron-builder": "^26.0.12", "electron-reload": "2.0.0-alpha.1", "postcss": "^8.5.3", "sass": "^1.86.0", "sass-loader": "^16.0.5", "tailwindcss": "^3.4.17", "unocss": "^0.53.5", "unplugin-auto-import": "^19.1.1", "unplugin-icons": "^0.17.0", "unplugin-vue-components": "^28.4.1", "unplugin-vue-setup-extend-plus": "^1.0.0", "vite": "^4.4.6", "vite-plugin-compression": "0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.2", "vite-plugin-mock": "^3.0.2", "vite-plugin-node-polyfills": "^0.23.0", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-inspector": "^3.7.1", "wait-on": "^8.0.3"}, "pnpm": {"onlyBuiltDependencies": ["electron"]}}