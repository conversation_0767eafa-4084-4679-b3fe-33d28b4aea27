{"info": {"title": "交通强国-船舶与海洋工程", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/shipstruct/user/login": {"post": {"summary": "用户登录", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"userId": {"type": "string", "title": "用户名"}, "password": {"type": "string", "title": "密码"}, "role": {"type": "string", "title": "角色", "description": "ccs用户版本，传值ccs，外部用户版，默认不传"}}, "required": ["userId", "password"]}}], "responses": {"200": {"description": "", "headers": {}, "schema": {"type": "object", "properties": {"code": {"type": "string"}, "msg": {"type": "string"}, "data": {"type": "string", "title": "登录token", "description": "登录token，需要再接口header中携带"}}, "required": ["code", "msg", "data"]}}}, "security": [], "consumes": ["application/json"], "produces": ["application/json"]}}, "/shipstruct/user/getUserInfo": {"get": {"summary": "获取用户信息", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "登录token", "required": false, "type": "string", "x-example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************.zwgmrsaQb0PRWWmwCEdLxW18O0R6m599ie2viskffLI"}], "responses": {"200": {"description": "", "headers": {}, "schema": {"type": "object", "properties": {"code": {"type": "string"}, "msg": {"type": "string"}, "data": {"type": "object", "properties": {"user": {"type": "object", "properties": {"userName": {"type": "string", "title": "用户名"}, "comGroup": {"type": "string", "title": "所属组织"}}, "required": ["userName", "comGroup"]}, "record": {"type": "object", "properties": {"loginTime": {"type": "string", "title": "最近一次登录时间", "description": "格式时间戳，需要改为yyyyMMdd hh:mm:ss"}}, "required": ["loginTime"]}}, "required": ["user", "record"]}}, "required": ["code", "msg", "data"]}}}, "security": [], "produces": ["application/json"]}}, "/shipstruct/user/changeUserInfo": {"post": {"summary": "修改用户信息", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "登录用户token", "required": false, "type": "string", "x-example": ""}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"userName": {"type": "string", "title": "新用户名"}, "comGroup": {"type": "string", "title": "所属组织"}, "password": {"type": "string", "title": "新密码"}}, "required": ["userName", "comGroup", "password"]}}], "responses": {"200": {"description": "", "headers": {}, "schema": {"type": "object", "properties": {"code": {"type": "string"}, "msg": {"type": "string"}, "data": {"type": "object", "properties": {}}}, "required": ["code", "msg", "data"]}}}, "security": [], "consumes": ["application/json"], "produces": ["application/json"]}}, "/shipstruct/ship/struct/addShip": {"post": {"summary": "添加船舶信息", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "登录token", "required": false, "type": "string", "x-example": ""}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"shipName": {"type": "string"}, "shipOwner": {"type": "string"}, "shipType": {"type": "string"}, "area": {"type": "string"}, "shipYard": {"type": "string"}, "imo": {"type": "string"}, "shipLoa": {"type": "number"}, "shipLpp": {"type": "number"}, "shipLrule": {"type": "number"}, "beamArch": {"type": "number"}, "moldDepth": {"type": "number"}, "moldWidth": {"type": "number"}, "structDraft": {"type": "number"}, "designDraft": {"type": "number"}, "dySwbmHogg": {"type": "number"}, "dySwbmSagg": {"type": "number"}, "staSwbmHogg": {"type": "number"}, "staSwbmSagg": {"type": "number"}, "dwt": {"type": "number"}, "lsw": {"type": "number"}, "ndw": {"type": "number"}, "csa": {"type": "string"}, "csm": {"type": "string"}, "keelDate": {"type": "string"}, "features": {"type": "object", "properties": {"特征1": {"type": "string"}, "特征2": {"type": "string"}}, "required": ["特征1", "01JZJ1AN1DSKFKFM196VKSDM5N", "特征2"], "x-nullable": true}}, "required": ["shipName", "imo", "shipYard", "area", "shipType", "shipOwner"]}}], "responses": {"200": {"description": "", "headers": {}, "schema": {"type": "object", "properties": {"code": {"type": "string"}, "msg": {"type": "string"}, "data": {"type": "string", "title": "返回船舶id"}}, "required": ["code", "msg"]}}}, "security": [], "consumes": ["application/json"], "produces": ["application/json"]}}, "/shipstruct/ship/struct/addLc": {"post": {"summary": "添加船舶装置工况", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "登录token", "required": false, "type": "string", "x-example": ""}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"id": {"type": "integer", "title": "船舶id", "description": "添加船舶信息后，返回的data id"}, "features": {"type": "object", "properties": {"许用值1": {"type": "string"}, "许用值2": {"type": "string"}}, "title": "许用值列表", "required": ["许用值1", "许用值2"], "description": "map格式的装载工况许用值，key为：许用值_工况1_其他描述，value为具体指"}}, "required": ["id", "features"]}}], "responses": {"200": {"description": "", "headers": {}, "schema": {"type": "object", "properties": {"code": {"type": "string"}, "msg": {"type": "string"}}, "required": ["code", "msg"]}}}, "security": [], "consumes": ["application/json"], "produces": ["application/json"]}}, "/shipstruct/ship/uploadReport": {"post": {"summary": "上传船舶图纸", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "登录token", "required": false, "type": "string", "x-example": ""}, {"name": "file", "in": "formData", "format": "binary", "type": "string"}, {"name": "shipId", "in": "formData", "type": "integer", "description": "船舶id"}, {"name": "type", "in": "formData", "type": "string", "description": "图纸类型"}, {"name": "subtitle", "in": "formData", "type": "string", "description": "摘要描述"}], "responses": {"200": {"description": "", "headers": {}, "schema": {"type": "object", "properties": {}}}}, "security": [], "consumes": ["multipart/form-data"], "produces": ["application/json"]}}, "/shipstruct/ship/admin/export": {"post": {"summary": "导出船舶信息", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "登录token", "required": false, "type": "string", "x-example": ""}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"shipName": {"type": "string", "title": "船名"}, "shipOwner": {"type": "string", "title": "船东"}, "shipType": {"type": "string", "title": "船舶类型"}, "area": {"type": "string", "title": "航区"}, "shipYard": {"type": "string", "title": "船厂"}, "imo": {"type": "string", "title": "imo号"}, "minShipLoa": {"type": "number", "title": "最小船长"}, "maxShipLoa": {"type": "number", "title": "最大船长"}, "minDwt": {"type": "number", "title": "最小排水量"}, "maxDwt": {"type": "number", "title": "最大排水量"}, "minMoldWidth": {"type": "number", "title": "最小型宽"}, "maxMoldWidth": {"type": "number"}, "minMoldDepth": {"type": "number", "title": "最小型深"}, "maxMoldDepth": {"type": "number"}, "minStructDraft": {"type": "number", "title": "最小结构吃水"}, "maxStructDraft": {"type": "number"}, "minDesignDraft": {"type": "number", "title": "最小设计吃水"}, "maxDesignDraft": {"type": "number"}, "csa": {"type": "string", "title": "入级符号-结构和设备"}, "csm": {"type": "string", "title": "入级符号-机械轮机和电气"}, "features": {"type": "object", "properties": {"特征1": {"type": "string"}, "特征2": {"type": "string"}}, "title": "特征"}}}}], "responses": {"200": {"description": "", "headers": {}, "schema": {"type": "object", "properties": {}}}}, "security": [], "consumes": ["application/json"], "produces": ["application/json"]}}, "/shipstruct/admin/ship/exportrecord/getRecords": {"get": {"summary": "导出记录", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "exportTimeStart", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "exportTimeEnd", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "Authorization", "in": "header", "description": "登录token", "required": false, "type": "string", "x-example": ""}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"exportTimeStart": {"type": "string", "title": "导出开始时间"}, "exportTimeEnd": {"type": "string", "title": "导出结束时间"}}, "required": ["exportTimeStart", "exportTimeEnd"]}}], "responses": {"200": {"description": "", "headers": {}, "schema": {"type": "object", "properties": {"code": {"type": "string"}, "msg": {"type": "string"}, "data": {"type": "object", "properties": {"total": {"type": "string", "title": "总数量"}, "pageNum": {"type": "string", "title": "总页数"}, "rows": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "exportTime": {"type": "string", "title": "导出时间"}, "verifyCode": {"type": "string", "title": "验证码"}, "filePath": {"type": "string", "title": "文件"}}, "required": ["id"]}, "title": "记录列表"}}, "required": ["total", "rows", "pageNum"]}}, "required": ["code", "data", "msg"]}}}, "security": [], "consumes": ["application/json"], "produces": ["application/json"]}}, "/shipstruct/admin/ship/exportrecord/download": {"post": {"summary": "下载导出文件", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "id", "in": "query", "description": "导出记录id", "required": false, "type": "integer"}, {"name": "Authorization", "in": "header", "description": "登录token", "required": false, "type": "string", "x-example": ""}], "responses": {"200": {"description": "", "headers": {}, "schema": {"type": "object", "properties": {}}}}, "security": [], "produces": ["application/octet-stream"]}}, "/shipstruct/ship/struct/getShipDetail": {"get": {"summary": "获取船舶详情", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "id", "in": "query", "description": "船舶id", "required": false, "type": "integer"}, {"name": "Authorization", "in": "header", "description": "登录token", "required": false, "type": "string", "x-example": ""}], "responses": {"200": {"description": "", "headers": {}, "schema": {"type": "object", "properties": {}}}}, "security": [], "produces": ["application/json"]}}, "/shipstruct/ship/getReports": {"post": {"summary": "获取船舶关联图纸，报告等上传附件信息", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "登录token", "required": false, "type": "string", "x-example": ""}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"shipId": {"type": "integer", "title": "船舶id"}, "reportType": {"type": "string", "title": "图纸类型", "description": "船舶结构、计算模型、评估报告、装载信息"}, "subTitle": {"type": "string", "title": "图纸摘要"}}, "required": ["shipId", "subTitle", "reportType"]}}], "responses": {"200": {"description": "", "headers": {}, "schema": {"type": "object", "properties": {}}}}, "security": [], "consumes": ["application/json"], "produces": ["application/json"]}}, "/shipstruct/ship/import": {"post": {"summary": "数据导入", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "verifyCode", "in": "query", "description": "zip解压密码", "required": false, "type": "string"}, {"name": "Authorization", "in": "header", "description": "登录token", "required": false, "type": "string", "x-example": ""}, {"name": "file", "in": "formData", "format": "binary", "type": "string"}], "responses": {"200": {"description": "", "headers": {}, "schema": {"type": "object", "properties": {}}}}, "security": [], "consumes": ["multipart/form-data"], "produces": ["application/json"]}}, "/shipstruct/ship/apply": {"post": {"summary": "数据申请", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "登录token", "required": false, "type": "string", "x-example": ""}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"toAccount": {"type": "string", "title": "收件人"}, "subject": {"type": "string", "title": "邮件主题"}, "content": {"type": "string", "title": "邮件内容"}}, "required": ["toAccount", "content", "subject"]}}], "responses": {"200": {"description": "", "headers": {}, "schema": {"type": "object", "properties": {}}}}, "security": [], "consumes": ["application/json"], "produces": ["application/json"]}}, "/shipstruct/ship/struct/statisByType": {"post": {"summary": "按船舶类型统计", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "登录token", "required": false, "type": "string", "x-example": ""}], "responses": {"200": {"description": "", "headers": {}, "schema": {"type": "object", "properties": {}}}}, "security": [], "produces": ["application/json"]}}, "/shipstruct/ship/struct/getAllShips": {"post": {"summary": "获取船舶列表", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "pageNo", "in": "query", "description": "当前页码，不设置页数据大小。默认20，不允许调整", "required": false, "type": "string"}, {"name": "Authorization", "in": "header", "description": "登录token", "required": false, "type": "string", "x-example": ""}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"shipName": {"type": "string"}, "shipOwner": {"type": "string"}, "shipType": {"type": "string"}, "area": {"type": "string"}, "shipYard": {"type": "string"}, "imo": {"type": "string"}, "minShipLoa": {"type": "integer"}, "maxShipLoa": {"type": "integer"}, "minDwt": {"type": "integer"}, "maxDwt": {"type": "integer"}, "minMoldWidth": {"type": "integer"}, "maxMoldWidth": {"type": "integer"}, "minMoldDepth": {"type": "integer"}, "maxMoldDepth": {"type": "integer"}, "minStructDraft": {"type": "integer"}, "maxStructDraft": {"type": "integer"}, "minDesignDraft": {"type": "integer"}, "maxDesignDraft": {"type": "integer"}, "csa": {"type": "string"}, "csm": {"type": "string"}, "features": {"type": "object", "properties": {"特征1": {"type": "string"}, "特征2": {"type": "string"}}, "required": ["特征1", "特征2"]}}, "required": ["shipName", "shipOwner", "shipType", "area", "shipYard", "imo", "minShipLoa", "maxShip<PERSON><PERSON>", "minDwt", "maxDwt", "minMold<PERSON>idth", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "minMoldDepth", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "minStructDraft", "maxStructDraft", "minDesignDraft", "maxDesignDraft", "csa", "csm", "features"]}}], "responses": {"200": {"description": "", "headers": {}, "schema": {"type": "object", "properties": {"code": {"type": "string"}, "msg": {"type": "string"}, "data": {"type": "object", "properties": {"total": {"type": "integer", "title": "数据总数"}, "pageNum": {"type": "integer", "title": "总页数"}, "rows": {"type": "array", "items": {"type": "object", "properties": {"shipName": {"type": "string"}, "shipOwner": {"type": "string"}}, "required": ["shipName", "shipOwner"]}}}, "required": ["total", "pageNum", "rows"]}}, "required": ["code", "msg"]}}}, "security": [], "consumes": ["application/json"], "produces": ["application/json"]}}, "/predict": {"post": {"summary": "未命名接口", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {}}}], "responses": {"200": {"description": "", "headers": {}, "schema": {"type": "object", "properties": {}}}}, "security": [], "consumes": ["application/json"], "produces": ["application/json"]}}, "/shipstruct/ship/getLatestShip": {"get": {"summary": "获取最新添加的船舶信息", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "登录token", "required": false, "type": "string", "x-example": ""}], "responses": {"200": {"description": "", "headers": {}, "schema": {"type": "object", "properties": {"code": {"type": "string"}, "msg": {"type": "string"}, "data": {"type": "object", "properties": {"ship": {"type": "object", "properties": {}, "title": "船舶基本信息", "description": "和船舶详情页一致"}, "report": {"type": "object", "properties": {}, "title": "报告图纸", "description": "和概要信息一致"}, "allowStress": {"type": "object", "properties": {}, "title": "许用值", "description": "和概要信息一致"}}, "required": ["ship", "allowStress", "report"]}}, "required": ["code", "data", "msg"]}}}, "security": [], "produces": ["application/json"]}}, "/shipstruct/ship/getSummary": {"get": {"summary": "详情页船舶概要信息", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "shipId", "in": "query", "description": "船舶id", "required": false, "type": "string"}, {"name": "Authorization", "in": "header", "description": "登录token", "required": false, "type": "string", "x-example": ""}], "responses": {"200": {"description": "", "headers": {}, "schema": {"type": "object", "properties": {"code": {"type": "string"}, "msg": {"type": "string"}, "data": {"type": "object", "properties": {"report": {"type": "array", "items": {"type": "object", "properties": {"subTitle": {"type": "string", "title": "图纸摘要信息", "description": "如：船舶横剖面图"}, "filePath": {"type": "string", "title": "图纸本地路径"}}, "required": ["subTitle", "filePath"]}}, "allowStress": {"type": "array", "items": {"type": "object", "properties": {"工况1_临界许用值": {"type": "string"}, "工况1_最小许用值": {"type": "string"}}, "required": ["工况1_临界许用值", "工况1_最小许用值"], "description": "包含多种工况的多种许用值列表"}}}, "required": ["report", "allowStress"]}}, "required": ["code", "data", "msg"]}}}, "security": [], "produces": ["application/json"]}}}, "security": [], "swagger": "2.0", "definitions": {}, "securityDefinitions": {}, "x-components": {}}