import { loadEnv } from 'vite'
import path from 'path'

/**
 * 获取系统名称
 * @param {string} mode - 当前构建模式
 * @returns {string} 系统名称
 */
export function getSystemName(mode) {
    const env = loadEnv(mode, process.cwd(), '')
    return env.VITE_SYSTEM_NAME || '默认系统名称'
}

/**
 * 获取所有环境变量
 * @param {string} mode - 当前构建模式
 * @returns {object} 环境变量对象
 */
export function getEnvVariables(mode) {
    return loadEnv(mode, process.cwd(), '')
}
