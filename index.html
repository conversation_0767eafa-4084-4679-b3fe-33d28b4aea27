<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/materials.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>
    <%= title %>
  </title>
</head>

<body>
  <div id="app">
    <style>
      @keyframes gradient {
        0% {
          background-position: 0% 50%;
        }

        50% {
          background-position: 100% 50%;
        }

        100% {
          background-position: 0% 50%;
        }
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
        }

        to {
          opacity: 1;
        }
      }

      @keyframes pulse {
        0% {
          transform: scale(0.95);
          box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
        }

        70% {
          transform: scale(1);
          box-shadow: 0 0 0 15px rgba(24, 144, 255, 0);
        }

        100% {
          transform: scale(0.95);
          box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
        }
      }

      @keyframes spinner {
        to {
          transform: rotate(360deg);
        }
      }

      @keyframes shimmer {
        0% {
          background-position: -468px 0;
        }

        100% {
          background-position: 468px 0;
        }
      }

      @keyframes progressGrow {
        0% {
          width: 5%;
        }

        20% {
          width: 15%;
        }

        40% {
          width: 30%;
        }

        60% {
          width: 45%;
        }

        80% {
          width: 65%;
        }

        95% {
          width: 85%;
        }

        100% {
          width: 95%;
        }
      }

      @keyframes progressPulse {
        0% {
          box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
        }

        70% {
          box-shadow: 0 0 0 5px rgba(24, 144, 255, 0);
        }

        100% {
          box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
        }
      }

      @keyframes floatingCircles {
        0% {
          transform: translateY(0) rotate(0deg);
        }

        50% {
          transform: translateY(-20px) rotate(180deg);
        }

        100% {
          transform: translateY(0) rotate(360deg);
        }
      }

      .app-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: linear-gradient(-45deg, #1a4a91, #0080b3, #0095a8, #00809e);
        background-size: 400% 400%;
        animation: gradient 15s ease infinite;
        overflow: hidden;
      }

      .geometric-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;
        opacity: 0.1;
        background-image: radial-gradient(circle at 25px 25px, white 2%, transparent 0%),
          radial-gradient(circle at 75px 75px, white 2%, transparent 0%);
        background-size: 100px 100px;
      }

      .floating-circle {
        position: absolute;
        border-radius: 50%;
        opacity: 0.15;
        background: linear-gradient(45deg, #ffffff, rgba(255, 255, 255, 0.5));
        animation: floatingCircles 12s infinite ease-in-out;
      }

      .floating-circle:nth-child(1) {
        width: 200px;
        height: 200px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      .floating-circle:nth-child(2) {
        width: 150px;
        height: 150px;
        top: 65%;
        left: 15%;
        animation-delay: -3s;
      }

      .floating-circle:nth-child(3) {
        width: 120px;
        height: 120px;
        top: 25%;
        left: 65%;
        animation-delay: -6s;
      }

      .floating-circle:nth-child(4) {
        width: 180px;
        height: 180px;
        top: 70%;
        left: 80%;
        animation-delay: -9s;
      }

      .app-loading-card {
        background: rgba(255, 255, 255, 0.85);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        padding: 40px;
        max-width: 550px;
        width: 90%;
        display: flex;
        flex-direction: column;
        align-items: center;
        animation: fadeIn 1s ease-out;
        border: 1px solid rgba(255, 255, 255, 0.4);
        position: relative;
        z-index: 10;
      }

      .app-loading-logo {
        width: 140px;
        height: 140px;
        margin-bottom: 30px;
        animation: pulse 2s infinite;
        border-radius: 50%;
        padding: 10px;
        background: white;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
      }

      .app-loading-title {
        font-size: 28px;
        font-weight: bold;
        color: #1890ff;
        margin-bottom: 8px;
        text-align: center;
      }

      .app-loading-subtitle {
        font-size: 16px;
        color: #666;
        margin-bottom: 30px;
        text-align: center;
      }

      .app-loading-progress {
        width: 100%;
        height: 6px;
        background-color: #f0f0f0;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 20px;
        position: relative;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      .app-loading-progress-bar {
        height: 100%;
        width: 10%;
        background: linear-gradient(90deg, #1890ff, #36cfc9);
        border-radius: 10px;
        animation:
          shimmer 2s infinite linear,
          progressGrow 8s ease-in-out forwards,
          progressPulse 2s infinite;
        background-size: 600px 100%;
        background-image: linear-gradient(to right, #1890ff 0%, #36cfc9 20%, #1890ff 40%, #1890ff 100%);
        position: relative;
      }

      .app-loading-progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.3) 50%,
            rgba(255, 255, 255, 0) 100%);
        animation: shimmer 1.5s infinite;
      }

      .app-loading-text {
        font-size: 18px;
        color: #333;
        margin-bottom: 24px;
      }

      .app-loading-spinner {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .spinner-dot {
        width: 12px;
        height: 12px;
        margin: 0 6px;
        border-radius: 50%;
        display: inline-block;
        background-color: #1890ff;
      }

      .spinner-dot:nth-child(1) {
        animation: bounce 1.4s ease-in-out infinite;
      }

      .spinner-dot:nth-child(2) {
        animation: bounce 1.4s ease-in-out 0.2s infinite;
      }

      .spinner-dot:nth-child(3) {
        animation: bounce 1.4s ease-in-out 0.4s infinite;
      }

      @keyframes bounce {

        0%,
        80%,
        100% {
          transform: scale(0);
          opacity: 0.3;
        }

        40% {
          transform: scale(1);
          opacity: 1;
        }
      }

      .app-loading-features {
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-top: 30px;
      }

      .feature-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        padding: 0 10px;
      }

      .feature-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        background: #f0f5ff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        color: #1890ff;
        font-size: 20px;
        font-weight: bold;
      }

      .feature-text {
        font-size: 12px;
        color: #666;
        text-align: center;
      }
    </style>
    <div class="app-loading">
      <div class="geometric-bg"></div>
      <div class="floating-circle"></div>
      <div class="floating-circle"></div>
      <div class="floating-circle"></div>
      <div class="floating-circle"></div>
      <div class="app-loading-card">
        <img class="app-loading-logo" src="/materials.svg" alt="Logo" />
        <div class="app-loading-title">船舶与海洋工程结构数据分析及支持服务平台</div>
        <div class="app-loading-subtitle">专业的船舶与海洋工程结构数据管理与分析平台</div>

        <div class="app-loading-progress">
          <div class="app-loading-progress-bar"></div>
        </div>

        <div class="app-loading-text">系统初始化中，请稍候...</div>

        <div class="app-loading-spinner">
          <div class="spinner-dot"></div>
          <div class="spinner-dot"></div>
          <div class="spinner-dot"></div>
        </div>

        <div class="app-loading-features">
          <div class="feature-item">
            <div class="feature-icon">Q</div>
            <div class="feature-text">船舶查询</div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">A</div>
            <div class="feature-text">数据申请</div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">I</div>
            <div class="feature-text">数据录入</div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">E</div>
            <div class="feature-text">数据导出</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>